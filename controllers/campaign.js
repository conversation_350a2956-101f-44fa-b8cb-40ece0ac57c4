var uuid = require('node-uuid')
var fs = require('fs')
var path = require('path')
var Sequelize = require('sequelize')
var Promise = Sequelize.Promise
var statsAPI = require('../database/redisAPI')('campaign')
var fileUtils = require('../utils/fileUtils.js')
var ensureDirectoryExists = fileUtils.ensureDirectoryExists
var copyFile = fileUtils.copyFile
var thread = require('../thread-scripts/threadWrapper')
var APP_CONFIG = require('../config/constants')
var _ = require('underscore')
var moment = require('moment')
var kue = require('kue'),
    queue = kue.createQueue(APP_CONFIG.KUE)
var transitionlead = require('../data-merge/transitionLead')

module.exports = function (app, Models, BASE_URL, db) {
    // Get campaigns list
    app.get(BASE_URL + '/campaigns', function (req, res) {
        var where = {}
        if (req.user.isClientAgent || req.user.isClientAdmin) {
            where.clientId = req.user.clientId
        }
        Models.Campaign.findAll({
            where,
            include: [Models.CampaignType, Models.Client, Models.CampaignGoals]
        }).then(function (items) {
            res.status(200).send(items)
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    // Get campaign by id
    app.get(BASE_URL + '/campaigns/:id', function (req, res) {
        var where = {
            id: req.params.id
        }
        if (req.user.isClientAgent || req.user.isClientAdmin) {
            where.clientId = req.user.clientId
        }
        Models.Campaign.find({
            where,
            include: [Models.CampaignType, Models.CampaignProjections, Models.Client, Models.CampaignGoals]
        }).then(function (result) {
            res.status(200).send(result)
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    // Get campaign training docs by campaign id
    app.get(BASE_URL + '/campaigns/:id/trainingdocs', function (req, res) {
        Models.CampaignTrainingDoc.findAll({
            where: {
                campaignId: req.params.id
            }
        })
            .then(function (result) {
                if (req.query && req.query.countOnly)
                    res.status(200).send({
                        count: result.length
                    })
                else
                    res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/products', function (req, res) {
        Models.CampaignProduct.findAll({
            where: {
                campaignId: req.params.id
            }
        })
            .then(function (result) {
                if (req.query && req.query.countOnly)
                    res.status(200).send({
                        count: result.length
                    })
                else
                    res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/invoices', function (req, res) {
        Models.Invoice.findAll({
            where: {
                campaignId: req.params.id
            },
            include: [Models.Lead, Models.CallResult, Models.Client, Models.Campaign]
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/callrecords', function (req, res) {
        Models.CallRecord.findAll({
            where: {
                campaignId: req.params.id
            },
            include: [Models.Lead, Models.CallResult, Models.Agent, Models.CampaignStage, Models.Campaign],
            order: 'createdAt DESC'
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/products/page/:page', function (req, res) {
        var promises = []
        promises.push(Models.CampaignProduct.findAll({
            limit: 30,
            offset: 30 * req.params.page,
            order: req.query.orderby + ' ' + req.query.dir,
            where: {
                campaignId: req.params.id
            }
        }))

        promises.push(Models.CampaignProduct.count({
            where: {
                campaignId: req.params.id
            }
        }))

        Promise.all(promises)
            .then(function (result) {
                res.status(200).send({
                    data: {
                        products: result[0],
                        count: result[1]
                    }
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get invoice by campaign and lead
    app.get(BASE_URL + '/campaigns/:id/leads/:leadid/invoices', function (req, res) {
        Models.Invoice.findAll({
            where: {
                campaignId: req.params.id,
                leadId: req.params.leadid
            },
            include: [
                { model: Models.CallResult, include: [Models.RecurringPayment] },
                Models.PaymentLog
            ]
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })



    // Get campaign progress by campaign id
    app.get(BASE_URL + '/campaigns/:id/progress', function (req, res) {
        var promises = []

        promises.push(statsAPI.get(req.params.id, 'totalGiftAmount'))
        promises.push(statsAPI.get(req.params.id, 'totalSaleAmount'))

        Promise.all(promises).then(function (result) {
            res.send({
                giftAmount: result[0] || 0,
                saleAmount: result[1] || 0
            })
        })
            .catch(function (err) {
                return res.status(500).send({
                    error: err ? (err.message ? err.message : err) : 'Unknown Error'
                })
            })
    })

    // Get campaign stages by campaign id
    app.get(BASE_URL + '/campaigns/:id/stages', function (req, res) {
        Models.CampaignStage.findAll({
            where: {
                campaignId: req.params.id
            }
        })
            .then(function (result) {
                if (req.query && req.query.countOnly)
                    res.status(200).send({
                        count: result.length
                    })
                else
                    res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/campaignnotes', function (req, res) {
        Models.CampaignNote.findAll({
            where: {
                campaignId: req.params.id
            }
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/callbacks', function (req, res) {
        Models.Callback.findAll({
            where: {
                campaignId: req.params.id,
                deleted: {
                    $or: {
                        $ne: true,
                        $eq: null
                    }
                }
            },
            include: [Models.CallResult, Models.Agent, Models.Lead, Models.Campaign]
        })
            .then(function (callbacks) {
                res.status(200).send(callbacks)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/batchchangehistory', (req, res) => {
        var offset = parseInt(req.query.offset) || 0
        var limit = parseInt(req.query.limit) || 50
        var orderBy = req.query.orderBy || 'createdAt'
        var orderDir = req.query.orderDir || 'desc'
        var where = {
            campaignId: req.params.id
        }
        if (req.query.type) where.type = req.query.type
        Models.BatchChangeHistory.findAndCountAll({
            where,
            limit,
            offset,
            order: [[orderBy, orderDir]],
            include: [Models.User],
        }).then(results => {
            res.send(results)
        }).catch(err => {
            console.log(err)
            res.status(500).send(err ? err.message || err : 'ERROR')
        })
    })

    app.get(BASE_URL + '/campaigns/:id/batchchangehistory/:historyId/download', (req, res) => {
        Models.BatchChangeHistory.findById(req.params.historyId).then(history => {
            if (!history) return res.status(400).send('Batch Change History Not Found')
            res.download(history.file, history.filename)
        }).catch(err => {
            console.log(err)
            res.status(500).send(err ? err.message || err : 'ERROR')
        })
    })

    app.delete(BASE_URL + '/campaigns/:id/leads/delete', function (req, res) {
        Models.CallAttempt.destroy({
            where: {
                campaignId: req.params.id,
                leadId: req.query.leadId
            }
        })
            .then(function (result) {
                Models.Callback.destroy({
                    where: {
                        campaignId: req.params.id,
                        leadId: req.query.leadId
                    }
                })
                    .then(function (result) {
                        Models.CampaignLead.destroy({
                            where: {
                                campaignId: req.params.id,
                                leadId: req.query.leadId
                            }
                        })
                            .then(function (result) {
                                res.status(200).send(true)
                            })
                            .catch(function (err) {
                                return res.status(500).send({
                                    error: err.message
                                })
                            })
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })

            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get campaign leads by campaign id
    app.get(BASE_URL + '/campaigns/:id/leads', function (req, res) {
        if (req.query && req.query.countOnly) {
            Models.CampaignLead.count({
                where: {
                    campaignId: req.params.id
                }
            })
                .then(function (result) {
                    res.status(200).send({
                        count: result
                    })
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        } else {
            Models.Campaign.findById(req.params.id)
                .then(function (campaign) {
                    if (campaign) {
                        campaign.getLeads({
                            include: [{
                                model: Models.Skill,
                                as: 'tfSkill'
                            }, {
                                model: Models.SubSkill,
                                as: 'tfSubSkill'
                            }, {
                                model: Models.Skill,
                                as: 'tmSkill'
                            }, {
                                model: Models.SubSkill,
                                as: 'tmSubSkill'
                            }, Models.CardToken]
                        })
                            .then(function (leads) {
                                res.status(200).send(leads)
                            })
                    } else {
                        res.status(500).send({
                            error: 'Campaign not found'
                        })
                    }
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        }
    })

    // Get campaign lead skills by campaign id
    app.get(BASE_URL + '/campaigns/:id/leads/types', function (req, res) {
        Models.Campaign.findById(req.params.id, {
            include: [Models.CampaignType]
        })
            .then(function (campaign) {
                var skillType = (campaign.campaigntype.name === 'Telefunding' ? 'tfSkillId' : 'tmSkillId')
                db.query(`
					select * from skills where id in
						(select distinct(leads.${skillType}) from campaignleads
						left join leads on leads.id = campaignleads.leadId
						where campaignleads.campaignId = :campaignId)
				`, {
                    replacements: {
                        campaignId: campaign.id
                    },
                    type: Sequelize.QueryTypes.SELECT
                })
                    .then(function (skills) {
                        res.status(200).send(skills)
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get campaign lead skills by campaign id
    app.get(BASE_URL + '/campaigns/:id/leads/subtypes', function (req, res) {
        Models.Campaign.findById(req.params.id, {
            include: [Models.CampaignType]
        })
            .then(function (campaign) {
                var skillType = (campaign.campaigntype.name === 'Telefunding' ? 'tfSubSkillId' : 'tmSubSkillId')
                db.query(`
					select * from subskills where id in
						(select distinct(leads.${skillType}) from campaignleads
						left join leads on leads.id = campaignleads.leadId
						where campaignleads.campaignId = :campaignId)
				`, {
                    replacements: {
                        campaignId: campaign.id
                    },
                    type: Sequelize.QueryTypes.SELECT
                })
                    .then(function (skills) {
                        res.status(200).send(skills)
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/leads/:leadid', function (req, res) {
        Models.Invoice.findAll({
            where: {
                campaignId: req.params.id,
                leadId: req.params.leadid
            },
            include: [Models.CallResult]
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/projections', function (req, res) {
        Models.CampaignProjections.findAll({
            where: {
                campaignId: req.params.id
            }
        })
            .then(function (projections) {
                res.status(200).send(projections)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/campaignstage/:stageid/dispositions', function (req, res) {
        Models.CampaignStage.findOne({
            include: [{
                model: Models.Campaign,
                where: {
                    id: req.params.id
                },
                required: true
            }, Models.Disposition, Models.Agent],
            where: {
                id: req.params.stageid
            }
        })
            .then(function (stage) {
                res.status(200).send(stage)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/leads/page/:page', function (req, res) {
        Models.Campaign.findById(req.params.id, {
            attributes: ['id'],
            include: [Models.CampaignType]
        }).then(function (campaign) {
            if (req.query.filters) {
                req.query.filters = JSON.parse(req.query.filters)
            } else {
                req.query.filters = {}
            }

            delete req.query.filters.phone;

            var tfSubskillFilter = {}
            var tmSubskillFilter = {}
            var campaignLeadFilter = {
                campaignId: req.params.id
            }

            var includes = []

            if (req.query.prefilters) {
                req.query.prefilters = JSON.parse(req.query.prefilters)

                if (req.query.prefilters.campaignstageId) {
                    campaignLeadFilter.currentCampaignStageId = req.query.prefilters.campaignstageId
                }

                if (req.query.prefilters.subskillId) {
                    if (campaign.campaigntype.name === 'Telefunding') {
                        tfSubskillFilter.id = req.query.prefilters.subskillId
                        includes = [{
                            model: Models.Skill,
                            as: 'tfSkill'
                        }, {
                            model: Models.SubSkill,
                            as: 'tfSubSkill',
                            where: tfSubskillFilter
                        }, {
                            model: Models.CampaignLead,
                            where: campaignLeadFilter
                        }]
                    } else {
                        tmSubskillFilter.id = req.query.prefilters.subskillId
                        includes = [{
                            model: Models.Skill,
                            as: 'tmSkill'
                        }, {
                            model: Models.SubSkill,
                            as: 'tmSubSkill',
                            where: tmSubskillFilter
                        }, {
                            model: Models.CampaignLead,
                            where: campaignLeadFilter
                        }]
                    }
                } else {
                    includes = [{
                        model: Models.Skill,
                        as: 'tfSkill'
                    }, {
                        model: Models.SubSkill,
                        as: 'tfSubSkill'
                    }, {
                        model: Models.Skill,
                        as: 'tmSkill'
                    }, {
                        model: Models.SubSkill,
                        as: 'tmSubSkill'
                    }, {
                        model: Models.CampaignLead,
                        required: true,
                        where: campaignLeadFilter
                    }]
                }
            }

            includes.push({
                model: Models.Suppression,
                where: {
                    campaignId: req.params.id,
                    $or: [{
                        actualStartDate: {
                            $lte: new Date()
                        }
                    }, {
                        startDate: {
                            $gt: new Date()
                        }
                    }],
                    finished: false,
                },
                required: false
            })

            includes.push(Models.CardToken)
            includes.push(Models.RecurringPayment)

            return Models.Lead.findAndCount({
                include: includes,
                attributes: ['id', 'clientRef', 'dontContactUntil', 'tfSkillId', 'tfSubSkillId', 'tmSkillId', 'tmSubSkillId', 'first_name', 'last_name', 'salutation', 'suffix', 'spouse_name', 'company_name', 'address1', 'address2', 'address3', 'city', 'state', 'zip', 'phone_home', 'phone_mobile', 'phone_work', 'phone_workmobile', 'email', 'agentPortfolioTag'],
                where: req.query.filters,
                limit: 30,
                offset: 30 * req.params.page,
                order: req.query.orderby + ' ' + req.query.dir
            })
        }).then(result => {
            res.status(200).send({
                data: {
                    leads: result.rows,
                    count: result.count
                }
            })
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.get(BASE_URL + '/campaigns/:id/stats', function (req, res) {
        Models.Campaign.find({
            where: {
                id: req.params.id
            },
            include: [{
                model: Models.CallResult,
                attributes: [
                    [Sequelize.fn('YEAR', Sequelize.col('callresults.createdAt')), 'year'],
                    [Sequelize.fn('MONTHNAME', Sequelize.col('callresults.createdAt')), 'month'],
                    [Sequelize.fn('SUM', Sequelize.col('callresults.saleAmount')), 'saleAmount'],
                    [Sequelize.fn('SUM', Sequelize.col('callresults.giftAmount')), 'giftAmount']
                ]
            }, {
                model: Models.CampaignType
            }],
            group: [Sequelize.literal('MONTH(callresults.createdAt)'), Sequelize.literal('YEAR(callresults.createdAt)')],
            order: 'createdAt'
        })
            .then(function (campaign) {
                statsAPI.getAllStatsForObject(req.params.id)
                    .then(function (stats) {
                        campaign.dataValues.stats = {}
                        for (var prop in stats) {
                            var newProp = prop.replace('campaign-' + req.params.id + '-stat-', '')
                            if (newProp.indexOf('-') === -1) campaign.dataValues.stats[newProp] = stats[prop]
                        }
                        var promises = []

                        promises.push(Models.CallResult.find({
                            where: {
                                campaignId: req.params.id,
                                giftAmount: {
                                    $gt: 0
                                }
                            },
                            attributes: [
                                [Sequelize.literal('COUNT(DISTINCT(leadId))'), 'count']
                            ]
                        }))

                        promises.push(Models.CallResult.find({
                            where: {
                                campaignId: req.params.id,
                                saleAmount: {
                                    $gt: 0
                                }
                            },
                            attributes: [
                                [Sequelize.literal('COUNT(DISTINCT(leadId))'), 'count']
                            ]
                        }))

                        if (req.query.showAddOnGifts === 'true') {
                            promises.push(Models.CallResult.find({
                                where: {
                                    campaignId: {
                                        $not: req.params.id
                                    },
                                    clientId: campaign.clientId,
                                    createdAt: {
                                        $gt: campaign.startDate,
                                        $lt: campaign.endDate
                                    }
                                },
                                attributes: [
                                    [Sequelize.literal('SUM(giftAmount)'), 'gifts']
                                ]
                            }))
                        }

                        Promise.all(promises)
                            .then(function (results) {
                                campaign.dataValues.stats.numberOfPledges = results[0].count
                                campaign.dataValues.stats.numberOfSales = results[1].count
                                if (req.query.showAddOnGifts === 'true') {
                                    console.log((results[2] || { dataValues: {} }).dataValues.gifts)
                                    campaign.dataValues.stats.addOnGiftsDb = (results[2] || { dataValues: {} }).dataValues.gifts
                                }
                                res.status(200).send(campaign)
                            })
                            .catch(function (err) {
                                return res.status(500).send({
                                    error: err.message
                                })
                            })
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    //get campaign lead audits by campaign id
    app.get(BASE_URL + '/campaigns/:id/leads/audits', function (req, res) {
        Models.Campaign.findById(req.params.id)
            .then(function (campaign) {
                campaign.getLeads({
                    include: [{
                        model: Models.LeadAudit,
                        required: true
                    }],
                    offset: 30 * req.query.page,
                    limit: 30
                })
                    .then(function (result) {
                        res.status(200).send(result)
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/callhistory', function (req, res) {
        Models.CallResult.findAll({
            include: [Models.Lead, Models.Sale],
            where: {
                campaignId: req.params.id
            }
        })
            .then(function (results) {
                results.forEach(function (result) {
                    delete result.dataValues.creditCardNumber
                    delete result.dataValues.creditCardExpDate
                    delete result.dataValues.creditCardSecurityCode
                })
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/audit', function (req, res) {
        Models.CampaignUpdate.findAll({
            where: {
                campaignId: req.params.id
            }
        })
            .then(results => {
                results.forEach(r => {
                    if (r.dataValues.detail && r.dataValues.detail[0] == '{') {
                        r.dataValues.detail = JSON.parse(r.dataValues.detail)
                    }
                })
                res.status(200).send(results.reverse())
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/callhistory/pledges', function (req, res) {
        Models.CallResult.findAll({
            include: [Models.Lead],
            where: {
                campaignId: req.params.id,
                giftAmount: {
                    $gt: 0
                }
            }
        })
            .then(function (results) {
                results.forEach(function (result) {
                    delete result.dataValues.creditCardNumber
                    delete result.dataValues.creditCardExpDate
                    delete result.dataValues.creditCardSecurityCode
                })
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/callhistory/sales', function (req, res) {
        Models.CallResult.findAll({
            include: [Models.Lead, {
                model: Models.Sale,
                required: true
            }, Models.CallRecord],
            where: {
                campaignId: req.params.id
            }
        })
            .then(function (results) {
                results.forEach(function (result) {
                    delete result.dataValues.creditCardNumber
                    delete result.dataValues.creditCardExpDate
                    delete result.dataValues.creditCardSecurityCode
                })
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/callhistory/refusals', function (req, res) {
        Models.CallResult.findAll({
            include: [Models.Lead],
            where: {
                campaignId: req.params.id,
                refusalReason: {
                    $ne: null
                }
            }
        })
            .then(function (results) {
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/agents', function (req, res) {
        Models.Agent.findAll({
            include: [{
                model: Models.CampaignStage,
                required: true,
                where: {
                    campaignId: req.params.id
                }
            }, {
                model: Models.User
            }]
        })
            .then(function (results) {
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // get all agent targets by campaign Id
    app.get(BASE_URL + '/campaigns/:id/agenttargets', function (req, res) {
        Models.CampaignAgentTarget.findAll({
            where: {
                campaignId: req.params.id
            },
            include: [Models.Agent]
        })
            .then(function (targets) {
                res.status(200).send(targets)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.put(BASE_URL + '/campaigns/agenttargets', function (req, res) {
        Models.CampaignAgentTarget.findById(req.body.id)
            .then(function (target) {
                if (target) {
                    target.updateAttributes(req.body)
                        .then(function (result) {
                            res.status(200).send(result)
                        })
                }
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.post(BASE_URL + '/campaigns/agenttargets', function (req, res) {
        if (req.body.length) {
            //an array of object
            Models.CampaignAgentTarget.bulkCreate(req.body, {
                ignoreDuplicates: true
            })
                .then(function (result) {
                    res.status(200).send({
                        result: true
                    })
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        } else {
            var where = db.where(db.fn('DATE', db.col('start')), db.fn('DATE', req.body.start))
            where = db.and([where, { agentId: req.body.agentId }, { campaignId: req.body.campaignId }])
            Models.CampaignAgentTarget.findOne({
                where: where
            }).then(existing => {
                var promise
                if (existing) {
                    promise = existing.updateAttributes(req.body)
                } else {
                    promise = Models.CampaignAgentTarget.create(req.body)
                }
                promise.then(function (result) {
                    res.status(200).send({
                        result: true
                    })
                }).catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
            }).catch(err => {
                return res.status(500).send({
                    error: err.message
                })
            })
        }
    })

    app.get(BASE_URL + '/campaigns/:id/uploads', function (req, res) {
        Models.LeadImportHistory.findAll({
            where: {
                campaignId: req.params.id
            }
        })
            .then(function (results) {
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/uploads/:historyId', function (req, res) {
        Models.LeadImportHistory.findById(req.params.historyId).then(function (results) {
            res.status(200).send(results)
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.get(BASE_URL + '/campaigns/:id/suppressed', (req, res) => {
        Models.Suppression.findAll({
            where: {
                campaignId: req.params.id,
                finished: !!req.query.finished
            }
        }).then((results) => {
            res.status(200).send(results)
        }).catch((err) => {
            res.status(500).send({
                error: err.message
            })
        })
    })

    // Upload leads
    app.post(BASE_URL + '/campaigns/:id/uploadleads', function (req, res) {
        var filePath = req.files.file.path
        var targetDir = path.resolve('./leadUploads/' + req.params.id + '/')
        var destinationFilePath = path.join(targetDir, req.files.file.name)
        var clientsForTesting = []

        ensureDirectoryExists(targetDir, function (err) {
            copyFile(filePath, destinationFilePath, function (err) {
                Models.Campaign.findById(req.params.id, {
                    include: [Models.CampaignType]
                })
                    .then(function (campaign) {
                        Models.LeadImportHistory.create({
                            campaignId: req.params.id,
                            updateOnly: req.query.updateOnly,
                            name: req.files.file.originalname,
                            path: destinationFilePath
                        }).then(history => {

                            if (clientsForTesting.indexOf(campaign.clientId) > -1) {

                                queue.create('importLeads', {
                                    importId: history.id
                                }).removeOnComplete(true).save()

                                history = JSON.parse(JSON.stringify(history))
                                history.success = true
                                res.status(200).send(history)
                            } else {

                                var taskId = thread.start('importLeads', {
                                    importId: history.id,
                                    campaign: campaign,
                                    updateOnly: req.query.updateOnly,
                                    filePath: filePath
                                })

                                history.update({
                                    taskId
                                })

                                res.status(200).send({
                                    success: true,
                                    taskId: taskId
                                })
                            }

                        })

                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
        })
    })

    // Upload batch payments
    app.post(BASE_URL + '/campaigns/:id/batchpayments', function (req, res) {
        var filePath = req.files.file.path
        var filename = req.files.file.originalname
        var targetDir = path.resolve('./batchchanges/' + req.params.id + '/')
        var destinationFilePath = path.join(targetDir, req.files.file.name)
        var campaign

        ensureDirectoryExists(targetDir, function (err) {
            copyFile(filePath, destinationFilePath, function (err) {
                Models.Campaign.findById(req.params.id, {
                    include: [Models.CampaignType]
                })
                    .then(function (_campaign) {
                        campaign = _campaign
                        return Models.BatchChangeHistory.create({
                            campaignId: campaign.id,
                            userId: req.user.id,
                            file: destinationFilePath,
                            filename: filename,
                            type: 'batchpayments'
                        })
                    }).then(history => {
                        var taskId = thread.start('importPayments', {
                            historyId: history.id,
                            campaign: campaign,
                            userId: req.user.id,
                            filePath: filePath
                        })

                        res.status(200).send({
                            success: true,
                            taskId: taskId
                        })
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
        })
    })

    // Upload batch remove leads
    app.post(BASE_URL + '/campaigns/:id/batchexportinvoices', function (req, res) {
        var filePath = req.files.file.path
        var filename = req.files.file.originalname
        var targetDir = path.resolve('./batchchanges/' + req.params.id + '/')
        var destinationFilePath = path.join(targetDir, req.files.file.name)

        ensureDirectoryExists(targetDir, function (err) {
            copyFile(filePath, destinationFilePath, function (err) {
                Models.Campaign.findById(req.params.id, {
                    include: [Models.CampaignType]
                })
                .then(function (campaign) {
                    return Models.BatchChangeHistory.create({
                        campaignId: campaign.id,
                        userId: req.user.id,
                        file: destinationFilePath,
                        filename: filename,
                        stage: undefined,
                        type: 'batchexportinvoices'
                    })
                    .then(history => {
                        var taskId = thread.start('exportPaperInvoices', {
                            campaign: campaign,
                            filePath: filePath,
                            historyId: history.id
                        })

                        res.status(200).send({
                            success: true,
                            taskId: taskId
                        })
                    })      
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })              
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
            })
        })
    })

    // Upload batch remove leads
    app.post(BASE_URL + '/campaigns/:id/batchremoveleads', function (req, res) {
        var filePath = req.files.file.path
        var targetDir = path.resolve('./batchchanges/' + req.params.id + '/')
        var destinationFilePath = path.join(targetDir, req.files.file.name)

        ensureDirectoryExists(targetDir, function (err) {
            copyFile(filePath, destinationFilePath, function (err) {
                Models.Campaign.findById(req.params.id, {
                    include: [Models.CampaignType]
                })
                    .then(function (campaign) {
                        var taskId = thread.start('importRemoveLeads', {
                            campaign: campaign,
                            filePath: filePath
                        })

                        res.status(200).send({
                            success: true,
                            taskId: taskId
                        })
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
        })
    })

    // Upload batch change stage
    app.post(BASE_URL + '/campaigns/:id/batchchangestage/:stageId', function (req, res) {
        var filePath = req.files.file.path
        var targetDir = path.resolve('./batchchanges/' + req.params.id + '/')
        var destinationFilePath = path.join(targetDir, req.files.file.name)
        var campaign
        var filename = req.files.file.originalname

        ensureDirectoryExists(targetDir, function (err) {
            copyFile(filePath, destinationFilePath, function (err) {
                Models.Campaign.findById(req.params.id, {
                    include: [Models.CampaignType]
                }).then(function (_campaign) {
                    campaign = _campaign
                    return Models.BatchChangeHistory.create({
                        campaignId: campaign.id,
                        userId: req.user.id,
                        file: destinationFilePath,
                        filename: filename,
                        stage: req.params.stageId ? parseInt(req.params.stageId) : undefined,
                        type: 'batchchangestage'
                    })
                }).then(history => {
                    var taskId = thread.start('importChangeStages', {
                        historyId: history.id,
                        campaign: campaign,
                        filePath: filePath,
                        stageId: parseInt(req.params.stageId)
                    })

                    res.status(200).send({
                        success: true,
                        taskId: taskId
                    })
                }).catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
            })
        })
    })

    // Upload training docs
    app.post(BASE_URL + '/campaigns/:id/uploadtrainingdocs', function (req, res) {
        var filePath = req.files.file.path

        var targetDir = path.resolve(APP_CONFIG.TRAINING_DOCS_LOCATION + req.params.id + '/')
        var destinationFilePath = path.join(targetDir, req.files.file.name)

        ensureDirectoryExists(targetDir, function () {
            copyFile(filePath, destinationFilePath, function () {
                Models.CampaignTrainingDoc
                    .create({
                        path: destinationFilePath,
                        name: req.files.file.originalname,
                        campaignId: req.params.id
                    })
                    .then(function () {
                        res.status(200).send({
                            success: true
                        })
                    })
                    .catch(function (e) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
        })
    })

    // Transition campaign lead to a new campaign stage
    app.post(BASE_URL + '/campaigns/:id/transitionlead', function (req, res) {
        function errHandler(err) {
            return res.status(500).send({
                error: err.message
            })
        }

        if (!req.body.leadId) {
            return res.status(500).send({
                error: 'Missing required arguments'
            })
        }

        // transitionlead(Models, req.body.leadId, req.body.newCampaignStageId, req.params.id).then(() => {
        //     return res.status(200).send({
        //         success: true
        //     })
        // }).catch(errHandler)
        // return

        Models.CallAttempt.destroy({
            where: {
                leadId: req.body.leadId,
                campaignId: req.params.id
            }
        })
        .then(() => {
            if (!req.body.newCampaignStageId) {
                //just null the stageid and return
                //we need to log this happened in the campaignlead history
                Models.Callback.update({
                    deleted: true
                }, {
                    where: {
                        leadId: req.body.leadId
                    }
                }).catch(() => { })
                Models.CampaignLead.findOne({
                    where: {
                        leadId: req.body.leadId,
                        campaignId: req.params.id
                    }
                }).then(campaignlead => {
                    var history = []
                    if (campaignlead.campaignStageHistory) {
                        history = JSON.parse(campaignlead.campaignStageHistory)
                    }
                    if (campaignlead.currentCampaignStageId) {
                        history.push(campaignlead.currentCampaignStageId)
                    }
                    Models.CampaignLead.update({
                        currentCampaignStageId: null,
                        campaignStageHistory: JSON.stringify(history),
                        transitionDate: new Date()
                    }, {
                        where: {
                            leadId: req.body.leadId,
                            campaignId: req.params.id
                        },
                        individualHooks: true
                    }).then(() => {
                        return res.status(200).send({
                            success: true
                        })
                    }).catch(errHandler)
                }).catch(errHandler)
            }
            else {
                //check if there is a campaign lead for the campaignid assigned to the destination stage
                Models.CampaignLead.findOne({
                    where: {
                        leadId: req.body.leadId,
                        campaignId: req.params.id
                    }
                })
                .then(campaignlead => {
                    //get the campaign stage requested
                    Models.CampaignStage.findById(req.body.newCampaignStageId, {
                        include: [{
                            model: Models.Campaign,
                            include: [Models.CampaignType]
                        }]
                    })
                    .then(campaignstage => {
                        if (campaignstage && campaignstage.name === 'Return to Previous Stage') {
                            if (campaignlead && campaignlead.campaignStageHistory) {
                                var history = JSON.parse(campaignlead.campaignStageHistory)

                                if (history.length) {
                                    req.body.newCampaignStageId = history[history.length - 1]
                                    return Models.CampaignStage.findById(req.body.newCampaignStageId, {
                                        include: [{
                                            model: Models.Campaign,
                                            include: [Models.CampaignType]
                                        }]
                                    })
                                }
                            }
                        }
                        
                        return campaignstage
                    })
                    .then(campaignstage => {
                        //if there is not then create the campaign lead object
                        new Promise((resolve, reject) => {
                            if (!campaignlead) {
                                Models.CampaignLead.create({
                                    leadId: req.body.leadId,
                                    campaignId: campaignstage.campaignId
                                })
                                    .then(resolve)
                                    .catch(reject)
                            } else {
                                resolve(campaignlead)
                            }
                        })
                        .then(campaignlead => {
                            //if the campaign on the campaignlead is not the param campaign then delete all call attempts for that lead as well
                            //in this scenario we will also need to null the currentcampaignstageId on the originating campaign campaignlead

                            //this can all be done out of sequence as they dont matter
                            var promises = []
                            if (campaignstage.campaignId != req.params.id) {
                                promises.push(Models.CallAttempt.destroy({
                                    where: {
                                        leadId: req.body.leadId,
                                        campaignId: campaignlead.campaignId
                                    }
                                }))

                                promises.push(Models.CampaignLead.update({
                                    currentCampaignStageId: null,
                                    transitionDate: new Date()
                                }, {
                                    where: {
                                        leadId: req.body.leadId,
                                        campaignId: req.params.id
                                    },
                                    individualHooks: true
                                }))
                            } else {
                                promises.push(Promise.resolve())
                            }

                            Promise.all(promises).then(() => {
                                Models.Lead.findById(req.body.leadId).then(lead => {
                                    var skillType = (campaignstage.campaign.campaigntype.name === 'Telefunding' ? 'tfSubSkillId' : 'tmSubSkillId')
                                    var csSkillsBlacklist = []
                                    if (campaignstage.blacklistedSkills) {
                                        try {
                                            csSkillsBlacklist = JSON.parse(campaignstage.blacklistedSkills)
                                        } catch (e) {
                                            csSkillsBlacklist = []
                                        }
                                    }
                                    if (csSkillsBlacklist.indexOf(lead[skillType]) > -1)
                                        req.body.newCampaignStageId = campaignstage.blacklistCampaignstageId || null

                                    var history = []
                                    if (campaignlead.campaignStageHistory) {
                                        try {
                                            history = JSON.parse(campaignlead.campaignStageHistory)
                                        } catch (e) {
                                            history = []
                                        }
                                    }

                                    if (campaignlead.currentCampaignStageId) {
                                        history.push(campaignlead.currentCampaignStageId)
                                    }

                                    campaignlead.updateAttributes({
                                        transitionDate: new Date(),
                                        currentCampaignStageId: req.body.newCampaignStageId,
                                        campaignStageHistory: JSON.stringify(history)
                                    })

                                    if (req.body.newCampaignStageId && (lead.phone_home || lead.phone_mobile || lead.phone_work || lead.phone_workmobile)) {
                                        Models.CampaignStageDateTimeRule.findAll({
                                            include: [Models.DateTimeRuleSet],
                                            where: {
                                                subskillId: lead[skillType],
                                                campaignstageId: campaignstage.id
                                            }
                                        })
                                        .then(function (campaignStageDTRules) {
                                            var inserts = []
                                            campaignStageDTRules.forEach(function (campaignDTRule) {
                                                for (var i = 0; i < campaignDTRule.quantity; i++) {
                                                    var dtr = campaignDTRule.datetimeruleset
                                                    inserts.push({
                                                        startTime: dtr.startTime,
                                                        endTime: dtr.endTime,
                                                        startDate: (campaignDTRule.startDate || null),
                                                        endDate: (campaignDTRule.endDate || null),
                                                        monday: dtr.monday,
                                                        tuesday: dtr.tuesday,
                                                        wednesday: dtr.wednesday,
                                                        thursday: dtr.thursday,
                                                        friday: dtr.friday,
                                                        saturday: dtr.saturday,
                                                        sunday: dtr.sunday,
                                                        campaignId: campaignstage.campaignId,
                                                        campaignstageId: campaignstage.id,
                                                        createdFromDTUuid: campaignDTRule.uuid,
                                                        leadId: lead.id,
                                                        randomSelector: uuid.v4()
                                                    })
                                                }
                                            })
                                            Models.CallAttempt.bulkCreate(inserts)

                                            return res.status(200).send({
                                                success: true
                                            })
                                        })
                                        .catch(errHandler)
                                    }
                                    else {
                                        //just update the CampaignLead to set currentcampstage to null
                                        //we also want to update the history
                                        var history = []
                                        if (campaignlead.campaignStageHistory) {
                                            try {
                                                history = JSON.parse(campaignlead.campaignStageHistory)
                                            } catch (e) {
                                                history = []
                                            }
                                        }

                                        if (campaignlead.currentCampaignStageId) {
                                            history.push(campaignlead.currentCampaignStageId)
                                        }

                                        campaignlead.updateAttributes({
                                            campaignStageHistory: JSON.stringify(history),
                                            currentCampaignStageId: null
                                        })
                                        .then(function (result) {
                                            return res.status(200).send({
                                                success: true
                                            })
                                        })
                                        .catch(errHandler)
                                    }
                                })
                                .catch(errHandler)
                            })
                        })
                        .catch(errHandler)
                    })
                    .catch(errHandler)
                })
                .catch(errHandler)
            }
        })
    })

    // Create campaign
    app.post(BASE_URL + '/campaigns', function (req, res) {
        if (req.user.isClientAgent || req.user.isClientAdmin) {
            req.body.clientId = req.user.clientId || req.body.clientId
        }
        Models.Campaign.create(req.body)
            .then(function (result) {
                res.status(201).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Update campaign
    app.put(BASE_URL + '/campaigns/:id', function (req, res) {
        Models.Campaign.findById(req.params.id)
            .then(function (result) {
                if (req.body.goals) {
                    Models.CampaignGoals.findAll({
                        where: {
                            campaignId: req.params.id
                        }
                    })
                        .then(function (currentGoals) {
                            for (var prop in req.body.goals) {
                                var currentGoal = _.findWhere(currentGoals, {
                                    reportingGroup: prop
                                })
                                if (currentGoal) {
                                    currentGoal.updateAttributes({
                                        goal: req.body.goals[prop].goal,
                                        projectedQuantity: req.body.goals[prop].projectedQuantity
                                    })
                                } else {
                                    Models.CampaignGoals.create({
                                        campaignId: req.params.id,
                                        reportingGroup: prop,
                                        goal: req.body.goals[prop].goal,
                                        projectedQuantity: req.body.goals[prop].projectedQuantity
                                    })
                                }
                            }
                        })
                }
                result.updateAttributes(req.body)
                    .then(function (result) {
                        res.status(200).send(result)
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.put(BASE_URL + '/campaigns/:id/projections', function (req, res) {
        Models.Campaign.findById(req.params.id)
            .then(function (result) {
                for (var i = 0; i < req.body.length; i++) {
                    var projection = req.body[i]
                    if (projection.id) {
                        Models.CampaignProjections.findById(projection.id)
                            .then(function (pro) {
                                pro.updateAttributes(projection)
                            })
                    } else {
                        Models.CampaignProjections.create(projection)
                    }
                }
            })
    })

    // Delete campaign
    app.delete(BASE_URL + '/campaigns/:id', function (req, res) {
        Models.Campaign.destroy({
            where: {
                id: req.params.id
            }
        })
            .then(function (result) {
                res.status(200).send({
                    success: true
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.post(BASE_URL + '/campaigns/:id/uploadproducts', function (req, res) {
        var filePath = req.files.file.path

        Models.Campaign.findById(req.params.id)
            .then(function (campaign) {
                var taskId = thread.start('uploadCampaignProducts', {
                    campaign: campaign,
                    filePath: filePath
                })

                res.status(200).send({
                    success: true,
                    taskId: taskId
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaigns/:id/skillsandsubskills', function (req, res) {
        Models.Campaign.findById(req.params.id, {
            include: [Models.CampaignType]
        })
            .then(function (campaign) {
                var skillType = (campaign.campaigntype.name === 'Telefunding' ? 'tfSkillId' : 'tmSkillId')
                var subskillType = (campaign.campaigntype.name === 'Telefunding' ? 'tfSubSkillId' : 'tmSubSkillId')
                GetCampaignLeadTypes(campaign.id, campaign.campaigntype.name)
                    .then(function (leadTypes) {
                        var promises = []
                        leadTypes.forEach(function (leadType) {
                            promises.push(db.query(`
								SELECT 
								    COUNT(*) as count, s.name
								FROM
								    campaignleads cl
								        LEFT JOIN
								    leads l ON cl.leadId = l.id
										LEFT JOIN
									skills s on l.${skillType} = s.id
								WHERE
								    cl.campaignId = :campaignId AND l.${subskillType} = :leadTypeId
								GROUP BY l.${skillType}
								ORDER BY count DESC
								LIMIT 1
								`, {
                                replacements: {
                                    campaignId: campaign.id,
                                    leadTypeId: leadType.id
                                },
                                type: Sequelize.QueryTypes.SELECT
                            }))
                        })
                        Promise.all(promises)
                            .then(function (results) {
                                for (var i = 0; i < leadTypes.length; i++) {
                                    if (results[i].length) {
                                        leadTypes[i].reportingGroup = results[i][0].name
                                    } else {
                                        leadTypes[i].reportingGroup = null
                                    }
                                }
                                res.status(200).send(leadTypes)
                            })
                            .catch(function (err) {
                                return res.status(500).send({
                                    error: err.message
                                })
                            })
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.post(BASE_URL + '/campaigns/:id/callattemptanalysis', function (req, res) {
        Models.Campaign.findById(req.params.id, {
            include: [Models.CampaignType, {
                model: Models.CampaignStage,
                include: [Models.CampaignStageDateTimeRule]
            }]
        }).then(campaign => {
            Models.DateTimeRuleSet.findAll()
                .then(rules => {
                    Models.SubSkill.findAll()
                        .then(skills => {
                            console.log(req.body)
                            var callattemptQuery = ''
                            if (req.body && req.body.date) {
                                callattemptQuery = ` AND ${req.body.day} = 1 AND 
									(startDate < '${req.body.date}' OR startDate is null) AND 
									(endDate > '${req.body.date}' OR endDate is null) 
									AND starttime <= '${req.body.starttime}' 
									AND endtime >= '${req.body.starttime}' `
                            }

                            var promises = []

                            promises.push(db.query(`
								SELECT 
									    cl.currentCampaignStageId AS 'stageId',
									    l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}Subskillid AS 'skillId',
									    ca.createdfromdtuuid AS 'ruleId',
									    COUNT(*) AS 'attempts',
                                        COUNT(DISTINCT cl.leadid) AS 'leads'
								FROM
								    callattempts ca
								        LEFT JOIN
								    leads l ON l.id = ca.leadid
								        LEFT JOIN
								    subskills ss ON ss.id = l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}Subskillid
								        LEFT JOIN
								    campaignleads cl ON cl.leadid = l.id
								WHERE
								    ca.campaignid = ${req.params.id}
								    	AND cl.campaignid = ${req.params.id}
								        AND cl.currentcampaignstageid IS NOT NULL
								GROUP BY cl.currentCampaignStageId , ss.id , ca.createdfromdtuuid WITH ROLLUP`, {
                                type: Sequelize.QueryTypes.SELECT
                            }))

                            promises.push(db.query(`
								SELECT 
								    cl.currentCampaignStageId AS 'stageId',
								    l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}SubSkillId AS 'skillId',
								    COUNT(cl.leadid) AS 'leads',
								    SUM(CASE
								        WHEN
								            (SELECT 
								                    COUNT(id)
								                FROM
								                    callbacks
								                WHERE
								                    leadid = cl.leadid 
								                    	AND (deleted <> 1 or deleted is null) 
								                    	AND (expired <> 1 or expired is null) 
								                    	AND endDateTime > '${req.body.date ? req.body.date : moment().utc().format('YYYY-MM-DD HH:mm:ss')}') > 0
								        THEN
								            1
								        ELSE 0
								    END) AS 'callback',
								    SUM(CASE
								        WHEN l.dontContactUntil > '${req.body.date ? req.body.date : moment().utc().format('YYYY-MM-DD HH:mm:ss')}' THEN 1
								        ELSE 0
								    END) AS 'dontContactUntil',
								    SUM(CASE
								        WHEN
								            (l.dontContactUntil < '${req.body.date ? req.body.date : moment().utc().format('YYYY-MM-DD HH:mm:ss')}'
								                OR l.dontContactUntil IS NULL)
								                AND NOT ((NULLIF(l.phone_home, '') IS NULL)
									                AND (NULLIF(l.phone_mobile, '') IS NULL)
									                AND (NULLIF(l.phone_work, '') IS NULL)
									                AND (NULLIF(l.phone_workmobile, '') IS NULL))
								                AND (SELECT 
								                    COUNT(id)
								                FROM
								                    callbacks
								                WHERE
								                    leadid = cl.leadid 
								                    	AND (deleted <> 1 or deleted is null) 
								                    	AND (expired <> 1 or expired is null) 
								                    	AND endDateTime > '${req.body.date ? req.body.date : moment().utc().format('YYYY-MM-DD HH:mm:ss')}') = 0
								                AND (SELECT 
								                    COUNT(id)
								                FROM
								                    callattempts
								                WHERE
								                    leadid = cl.leadid
								                        AND campaignid = ${req.params.id} ${callattemptQuery}) > 0
								        THEN
								            1
								        ELSE 0
								    END) AS 'viable',
								    SUM(CASE
								        WHEN
								            (SELECT 
								                    COUNT(id)
								                FROM
								                    callattempts
								                WHERE
								                    leadid = cl.leadid AND campaignid = ${req.params.id}) = 0
									                AND NOT ((NULLIF(l.phone_home, '') IS NULL)
									                AND (NULLIF(l.phone_mobile, '') IS NULL)
									                AND (NULLIF(l.phone_work, '') IS NULL)
									                AND (NULLIF(l.phone_workmobile, '') IS NULL))
									                AND cl.currentCampaignStageId IS NOT NULL
								        THEN
								            1
								        ELSE 0
								    END) as 'exhausted',
								    SUM(CASE
								        WHEN
								            (NULLIF(l.phone_home, '') IS NULL)
								                AND (NULLIF(l.phone_mobile, '') IS NULL)
								                AND (NULLIF(l.phone_work, '') IS NULL)
								                AND (NULLIF(l.phone_workmobile, '') IS NULL)
								        THEN
								            1
								        ELSE 0
								    END) AS 'bad_numbers',
								    SUM(CASE
								    	WHEN cl.currentcampaignstageId IS NULL THEN 1 ELSE 0 END) as 'noStage'
								FROM
								    campaignleads cl
								        LEFT JOIN
								    leads l ON l.id = cl.leadid
								        LEFT JOIN
								    subskills ss ON ss.id = l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}subskillid
								WHERE
								    cl.campaignid = ${req.params.id}
								        AND l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}subskillid IS NOT NULL
								GROUP BY cl.currentcampaignstageid , l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}subskillid WITH ROLLUP`, {
                                type: Sequelize.QueryTypes.SELECT
                            }))

                            promises.push(db.query(`SELECT 
                                campaignstageId as 'stage', tfSubSkillId as 'skillId', COUNT(*) as 'count'
                                FROM
                                    suppressions s
                                        LEFT JOIN
                                    leads l ON l.id = s.leadId
                                WHERE
                                    s.campaignId = :campaignId
                                        AND s.finished = FALSE
                                        AND s.actualStartDate IS NOT NULL
                                        AND l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}subskillid IS NOT NULL
                                GROUP BY campaignstageid , l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}subskillid`, {
                                        type: Sequelize.QueryTypes.SELECT,
                                        replacements: {
                                            campaignId: campaign.id
                                        }
                            }))

                            Promise.all(promises)
                                .then(results => {
                                    var stages = []

                                    var totalAttempts = 0
                                    var totalLeads = 0
                                    var totalExhausted = 0
                                    var totalLeadsWithAttempts = 0
                                    var totalViable = 0
                                    var totalCallback = 0
                                    var totalDontContactUntil = 0
                                    var totalNoStage = 0
                                    var totalBadNumbers = 0

                                    var lastSkill
                                    var lastRule

                                    results[0].forEach(row => {
                                        try {
                                            if (!row.stageId) {
                                                totalAttempts = row.attempts
                                                totalLeadsWithAttempts = row.leads
                                                return
                                            }

                                            var stage = _.findWhere(campaign.campaignstages, {
                                                id: row.stageId
                                            })

                                            var skill = _.findWhere(skills, {
                                                id: row.skillId
                                            })

                                            var existingStage = _.findWhere(stages, {
                                                id: row.stageId
                                            })

                                            if (existingStage) {
                                                existingStage.leadsWithAttempts = row.leads
                                                existingStage.callAttempts = row.attempts

                                                var existingSkill = _.findWhere(existingStage.skills, {
                                                    id: row.skillId
                                                })

                                                if (!existingSkill) {
                                                    existingSkill = {
                                                        id: row.skillId,
                                                        name: skill ? skill.name : '',
                                                        callAttempts: row.attempts,
                                                        leadsWithAttempts: row.leads,
                                                        rules: []
                                                    }
                                                    if (!existingStage.skills) existingStage.skills = []
                                                    existingStage.skills.push(existingSkill)
                                                } else if (!row.ruleId && lastRule !== null) {
                                                    existingSkill.callAttempts = row.attempts
                                                    existingSkill.leadsWithAttempts = row.leads
                                                }

                                                if (row.ruleId) {
                                                    var stageRule = _.findWhere(stage.campaignstagedatetimerules, {
                                                        uuid: row.ruleId
                                                    })

                                                    if (stageRule) {
                                                        var rule = _.findWhere(rules, {
                                                            id: stageRule.datetimerulesetId
                                                        })

                                                        var existing = _.findWhere(existingSkill.rules, {
                                                            id: rule.id
                                                        })
                                                        if (!existing) {
                                                            if (!existingSkill.rules) existingSkill.rules = []
                                                            existingSkill.rules.push({
                                                                id: rule.id,
                                                                uuid: row.ruleId,
                                                                name: rule ? rule.name : '',
                                                                callAttempts: row.attempts
                                                            })
                                                        } else if (row.ruleId != existing.uuid) {
                                                            existing.callAttempts += row.attempts
                                                        }
                                                    }
                                                }
                                            } else {
                                                var newstage = {
                                                    id: row.stageId,
                                                    name: stage ? stage.name : '',
                                                    leadsWithAttempts: row.leads,
                                                    callAttempts: row.attempts,
                                                    skills: [{
                                                        id: row.skillId,
                                                        name: skill ? skill.name : '',
                                                        callAttempts: row.attempts,
                                                        leadsWithAttempts: row.leads,
                                                        rules: []
                                                    }]
                                                }
                                                if (row.ruleId) {
                                                    var stageRule = _.findWhere(stage.campaignstagedatetimerules, {
                                                        uuid: row.ruleId
                                                    })
                                                    if (stageRule) {
                                                        var rule = _.findWhere(rules, {
                                                            id: stageRule.datetimerulesetId
                                                        })

                                                        newstage.skills[0].rules.push({
                                                            id: row.ruleId,
                                                            name: rule ? rule.name : '',
                                                            callAttempts: row.attempts,
                                                            leadsWithAttempts: row.leads
                                                        })
                                                    }
                                                }
                                                stages.push(newstage)
                                            }

                                            lastSkill = row.skillId
                                            lastRule = row.ruleId
                                        } catch (err) {
                                            console.log(row)
                                            console.log(err)
                                        }
                                    })

                                    results[1].forEach(row => {
                                        try {
                                            if (!row.stageId && !row.skillId) {
                                                totalLeads = row.leads
                                                totalExhausted = row.exhausted
                                                totalViable = row.viable
                                                totalCallback = row.callback
                                                totalDontContactUntil = row.dontContactUntil
                                                totalNoStage = row.noStage
                                                totalBadNumbers = row.bad_numbers
                                                return
                                            }

                                            if (!row.stageId) {
                                                return
                                            }

                                            var stage = _.findWhere(campaign.campaignstages, {
                                                id: row.stageId
                                            })

                                            var skill = _.findWhere(skills, {
                                                id: row.skillId
                                            })

                                            var existingStage = _.findWhere(stages, {
                                                id: row.stageId
                                            })
                                            if (existingStage) {
                                                existingStage.leads = row.leads
                                                existingStage.exhausted = row.exhausted
                                                existingStage.viable = row.viable
                                                existingStage.dontContactUntil = row.dontContactUntil
                                                existingStage.callback = row.callback
                                                existingStage.badNumber = row.bad_numbers
                                                if (row.skillId) {
                                                    var existingSkill = _.findWhere(existingStage.skills, {
                                                        id: row.skillId
                                                    })
                                                    if (existingSkill) {
                                                        if (existingSkill.leads === undefined) {
                                                            existingSkill.leads = row.leads
                                                        }
                                                        existingSkill.exhausted = row.exhausted
                                                        existingSkill.viable = row.viable
                                                        existingSkill.dontContactUntil = row.dontContactUntil
                                                        existingSkill.callback = row.callback
                                                        existingSkill.badNumber = row.bad_numbers
                                                    } else {
                                                        if (!existingStage.skills) existingStage.skills = []
                                                        existingStage.skills.push({
                                                            id: row.skillId,
                                                            name: skill ? skill.name : 'No lead type found',
                                                            exhausted: row.exhausted,
                                                            leads: row.leads,
                                                            viable: row.viable,
                                                            dontContactUntil: row.dontContactUntil,
                                                            callback: row.callback,
                                                            badNumber: row.bad_numbers,
                                                            rules: []
                                                        })
                                                    }
                                                }
                                            } else {
                                                var newStage = {
                                                    id: row.stageId,
                                                    name: stage ? stage.name : 'No stage found',
                                                    exhausted: row.exhausted,
                                                    badNumber: row.bad_numbers
                                                }

                                                if (row.skillId) {
                                                    newStage.skills = [{
                                                        id: row.skillId,
                                                        name: skill ? skill.name : 'No lead type found',
                                                        exhausted: row.exhausted,
                                                        leads: row.leads,
                                                        viable: row.viable,
                                                        dontContactUntil: row.dontContactUntil,
                                                        callback: row.callback,
                                                        badNumber: row.bad_numbers,
                                                        rules: []
                                                    }]
                                                }

                                                stages.push(newStage)
                                            }
                                        } catch (err) {
                                            console.log(row)
                                            console.log(err)
                                        }
                                    })

                                    var suppressedNoStage = 0
                                    var suppressedTotal = 0

                                    results[2].forEach(row => {
                                        suppressedTotal += parseInt(row.count)
                                        if (row.stage) {

                                            var stage = _.findWhere(campaign.campaignstages, {
                                                id: row.stage
                                            })

                                            var skill = _.findWhere(skills, {
                                                id: row.skillId
                                            })

                                            var existingStage = _.findWhere(stages, {
                                                id: row.stage
                                            })
                                            if (existingStage) {
                                                if (!existingStage.suppressions) existingStage.suppressions = 0
                                                existingStage.suppressions += parseInt(row.count)

                                                var existingSkill = _.findWhere(existingStage.skills, {
                                                    id: row.skillId
                                                })
                                                if (existingSkill) {
                                                    if (!existingSkill.suppressions) existingSkill.suppressions = 0
                                                    existingSkill.suppressions += parseInt(row.count)
                                                }
                                                else {
                                                    if (!existingStage.skills) existingStage.skills = []
                                                    existingStage.skills.push({
                                                        id: row.skillId,
                                                        name: skill ? skill.name : 'No lead type found',
                                                        exhausted: 0,
                                                        leads: 0,
                                                        viable: 0,
                                                        dontContactUntil: 0,
                                                        callback: 0,
                                                        badNumber: 0,
                                                        rules: [],
                                                        suppressions: parseInt(row.count)
                                                    })
                                                }
                                            } else {
                                                var newStage = {
                                                    id: row.stage,
                                                    name: stage ? stage.name : 'No stage found',
                                                    exhausted: 0,
                                                    badNumber: 0
                                                }

                                                if (row.skill) {
                                                    newStage.skills = [{
                                                        id: row.skillId,
                                                        name: skill ? skill.name : 'No lead type found',
                                                        exhausted: 0,
                                                        leads: 0,
                                                        viable: 0,
                                                        dontContactUntil: 0,
                                                        callback: 0,
                                                        badNumber: 0,
                                                        rules: []
                                                    }]
                                                }

                                                stages.push(newStage)
                                            }
                                        } else {
                                            suppressedNoStage += parseInt(row.count)
                                        }
                                    })

                                    res.status(200).send({
                                        totalLeads: totalLeads,
                                        totalCallAttempts: totalAttempts,
                                        totalNoCallAttempts: totalExhausted,
                                        totalCallback: totalCallback,
                                        totalViable: totalViable,
                                        totalDontContactUntil: totalDontContactUntil,
                                        totalNoStage: totalNoStage - suppressedNoStage,
                                        totalBadNumbers: totalBadNumbers,
                                        stages: stages,
                                        suppressedNoStage: suppressedNoStage,
                                        suppressedTotal: suppressedTotal
                                    })
                                })
                                .catch(err => {
                                    console.log(err)
                                    return res.status(500).send({
                                        error: err.message
                                    })
                                })
                        })

                })
        })
    })

    function GetCampaignLeadTypes(campaignId, campaignType) {
        var skillType = (campaignType === 'Telefunding' ? 'tfSubSkillId' : 'tmSubSkillId')
        return (db.query(`
				select * from subskills where id in
					(select distinct(leads.${skillType}) from campaignleads
					left join leads on leads.id = campaignleads.leadId
					where campaignleads.campaignId = :campaignId)
				`, {
            replacements: {
                campaignId: campaignId
            },
            type: Sequelize.QueryTypes.SELECT
        }))
    }

    function GetCampaignReportingGroups(campaignId, campaignType) {
        var skillType = (campaignType === 'Telefunding' ? 'tfSkillId' : 'tmSkillId')
        return (db.query(`
				select * from skills where id in
					(select distinct(leads.${skillType}) from campaignleads
					left join leads on leads.id = campaignleads.leadId
					where campaignleads.campaignId = :campaignId)
				`, {
            replacements: {
                campaignId: campaignId
            },
            type: Sequelize.QueryTypes.SELECT
        }))
    }

    // Get dialing rules for campaign
    app.get(BASE_URL + '/campaigns/:id/dialingrules', function (req, res) {
        Models.Campaign.findById(req.params.id, {
            include: [{
                model: Models.CampaignStage,
                include: [{
                    model: Models.CampaignStageDateTimeRule,
                    include: [Models.DateTimeRuleSet]
                }]
            }]
        })
        .then(function (campaign) {
            if (!campaign) {
                return res.status(404).send({
                    error: 'Campaign not found'
                })
            }

            var dialingRules = []
            var ruleMap = new Map()

            var currentDate = new Date()

            campaign.campaignstages.forEach(stage => {
                stage.campaignstagedatetimerules.forEach(stageRule => {
                    if (stageRule.datetimeruleset && !ruleMap.has(stageRule.datetimeruleset.id)) {
                        // Check if rule is active (no date range set OR within date range)
                        var isActive = true
                        if (stageRule.startDate || stageRule.endDate) {
                            if (stageRule.startDate && currentDate < new Date(stageRule.startDate)) {
                                isActive = false
                            }
                            if (stageRule.endDate && currentDate > new Date(stageRule.endDate)) {
                                isActive = false
                            }
                        }

                        if (isActive) {
                            ruleMap.set(stageRule.datetimeruleset.id, {
                                id: stageRule.uuid,
                                name: stageRule.datetimeruleset.name,
                                description: stageRule.datetimeruleset.description || '',
                                startTime: stageRule.datetimeruleset.startTime,
                                endTime: stageRule.datetimeruleset.endTime,
                                daysOfWeek: stageRule.datetimeruleset.daysOfWeek
                            })
                        }
                    }
                })
            })

            var dialingRules = [];
            ruleMap.forEach(function(value, key) {
                dialingRules.push(value);
            });

            res.status(200).send(dialingRules)
        })
        .catch(function (err) {
            console.log(err)
            return res.status(500).send({
                error: err.message
            })
        })
    })

    // V2 Call Attempts Analysis Endpoint
    app.post(BASE_URL + '/campaigns/:id/callattemptanalysisv2', function (req, res) {
        Models.Campaign.findById(req.params.id, {
            include: [Models.CampaignType, {
                model: Models.CampaignStage,
                include: [Models.CampaignStageDateTimeRule]
            }]
        }).then(campaign => {
            if (!campaign) {
                return res.status(404).send({
                    error: 'Campaign not found'
                })
            }

            Models.DateTimeRuleSet.findAll()
                .then(rules => {
                    // Get skills (reporting groups) and subskills (lead types) separately
                    Promise.all([
                        GetCampaignReportingGroups(req.params.id, campaign.campaigntype.name),
                        GetCampaignLeadTypes(req.params.id, campaign.campaigntype.name)
                    ]).then(results => {
                        var skills = results[0]  // reporting groups (skills)
                        var subskills = results[1]  // lead types (subskills)

                        console.log('Skills (reporting groups):', skills);
                        console.log('Subskills (lead types):', subskills);
                        console.log(req.body)

                            var dialingRuleId = req.body.dialingRuleId
                            var agentId = req.body.agentId

                            if (dialingRuleId && typeof dialingRuleId !== 'string') {
                                return res.status(400).send({ error: 'Invalid dialingRuleId parameter' })
                            }

                            var parsedAgentId = parseInt(agentId, 10);

                            if (agentId && (isNaN(parsedAgentId) || parsedAgentId <= 0)) {
                                return res.status(400).send({ error: 'Invalid agentId parameter' })
                            }

                            // Use single timestamp for consistency
                            var currentDateTime = moment().format('YYYY-MM-DD HH:mm:ss')

                            // Build WHERE clause for agent portfolio filtering - use parameterized query
                            var agentWhere = ''
                            if (agentId) {
                                agentWhere = `AND l.agentPortfolioTag = (SELECT name FROM agents WHERE id = :agentId)`
                            }

                            var promises = []

                            // Query 1: Call attempts analysis
                            var query1Replacements = {
                                campaignId: req.params.id
                            }
                            if (agentId) {
                                query1Replacements.agentId = parseInt(agentId)
                            }

                            promises.push(db.query(`
                                SELECT
                                    cl.currentCampaignStageId AS 'stageId',
                                    l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}SkillId AS 'reportingGroupId',
                                    l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}Subskillid AS 'skillId',
                                    ca.createdfromdtuuid AS 'ruleId',
                                    COUNT(*) AS 'attempts',
                                    COUNT(DISTINCT cl.leadid) AS 'leads'
                                FROM callattempts ca
                                INNER JOIN campaignleads cl ON cl.leadid = ca.leadid AND cl.campaignid = :campaignId AND cl.currentcampaignstageid IS NOT NULL
                                INNER JOIN leads l ON l.id = ca.leadid
                                WHERE ca.campaignid = :campaignId
                                ${agentWhere}
                                GROUP BY cl.currentCampaignStageId, l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}SkillId, l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}Subskillid WITH ROLLUP`, {
                                type: Sequelize.QueryTypes.SELECT,
                                replacements: query1Replacements
                            }))

                            // Query 2: Call results count per lead
                            promises.push(db.query(`
                                SELECT cr.leadid, COUNT(*) as calls_made
                                FROM callresults cr
                                INNER JOIN leads l ON l.id = cr.leadid
                                WHERE cr.campaignid = :campaignId
                                ${agentWhere}
                                GROUP BY cr.leadid`, {
                                type: Sequelize.QueryTypes.SELECT,
                                replacements: query1Replacements
                            }))

                            // Query 3: Campaign leads with stage/skill info
                            promises.push(db.query(`
                                SELECT
                                    cl.leadid,
                                    cl.currentCampaignStageId AS 'stageId',
                                    l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}SkillId AS 'reportingGroupId',
                                    l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}Subskillid AS 'skillId'
                                FROM campaignleads cl
                                INNER JOIN leads l ON l.id = cl.leadid
                                WHERE cl.campaignid = :campaignId
                                  AND cl.currentcampaignstageid IS NOT NULL
                                  ${agentWhere}`, {
                                type: Sequelize.QueryTypes.SELECT,
                                replacements: query1Replacements
                            }))

                            // Query 4: Lead status breakdown
                            var query2Replacements = {
                                campaignId: req.params.id,
                                currentDateTime: currentDateTime
                            }
                            if (agentId) {
                                query2Replacements.agentId = parseInt(agentId)
                            }

                            promises.push(db.query(`
                                SELECT
                                    cl.currentCampaignStageId AS 'stageId',
                                    l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}SkillId AS 'reportingGroupId',
                                    l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}SubSkillId AS 'skillId',
                                    COUNT(cl.leadid) AS 'leads',
                                    SUM(CASE
                                        WHEN EXISTS (
                                            SELECT 1 FROM callbacks cb
                                            WHERE cb.leadid = cl.leadid
                                                AND (cb.deleted <> 1 OR cb.deleted IS NULL)
                                                AND (cb.expired <> 1 OR cb.expired IS NULL)
                                                AND cb.endDateTime > :currentDateTime
                                        ) THEN 1 ELSE 0
                                    END) AS 'callback',
                                    SUM(CASE WHEN l.dontContactUntil > :currentDateTime THEN 1 ELSE 0 END) AS 'dontContactUntil',
                                    SUM(CASE
                                        WHEN (l.dontContactUntil < :currentDateTime OR l.dontContactUntil IS NULL)
                                            AND NOT ((NULLIF(l.phone_home, '') IS NULL)
                                                AND (NULLIF(l.phone_mobile, '') IS NULL)
                                                AND (NULLIF(l.phone_work, '') IS NULL)
                                                AND (NULLIF(l.phone_workmobile, '') IS NULL))
                                            AND NOT EXISTS (
                                                SELECT 1 FROM callbacks cb2
                                                WHERE cb2.leadid = cl.leadid
                                                    AND (cb2.deleted <> 1 OR cb2.deleted IS NULL)
                                                    AND (cb2.expired <> 1 OR cb2.expired IS NULL)
                                                    AND cb2.endDateTime > :currentDateTime
                                            )
                                            AND NOT EXISTS (
                                                SELECT 1 FROM suppressions s2
                                                WHERE s2.leadid = cl.leadid
                                                    AND s2.campaignid = :campaignId
                                                    AND s2.finished = FALSE
                                                    AND s2.actualStartDate IS NOT NULL
                                            )
                                            AND EXISTS (
                                                SELECT 1 FROM callattempts ca3
                                                WHERE ca3.leadid = cl.leadid AND ca3.campaignid = :campaignId
                                                LIMIT 1
                                            )
                                        THEN 1 ELSE 0
                                    END) AS 'viable',
                                    SUM(CASE
                                        WHEN NOT EXISTS (
                                                SELECT 1 FROM callattempts ca4
                                                WHERE ca4.leadid = cl.leadid AND ca4.campaignid = :campaignId LIMIT 1
                                            )
                                            AND EXISTS (
                                                SELECT 1 FROM callresults cr2
                                                WHERE cr2.leadid = cl.leadid AND cr2.campaignid = :campaignId LIMIT 1
                                            )
                                            AND NOT ((NULLIF(l.phone_home, '') IS NULL)
                                            AND (NULLIF(l.phone_mobile, '') IS NULL)
                                            AND (NULLIF(l.phone_work, '') IS NULL)
                                            AND (NULLIF(l.phone_workmobile, '') IS NULL))
                                            AND cl.currentCampaignStageId IS NOT NULL
                                        THEN 1 ELSE 0
                                    END) as 'exhausted',
                                    SUM(CASE
                                        WHEN (NULLIF(l.phone_home, '') IS NULL)
                                            AND (NULLIF(l.phone_mobile, '') IS NULL)
                                            AND (NULLIF(l.phone_work, '') IS NULL)
                                            AND (NULLIF(l.phone_workmobile, '') IS NULL)

                                        THEN 1 ELSE 0
                                    END) AS 'bad_numbers',
                                    SUM(CASE WHEN cl.currentcampaignstageId IS NULL THEN 1 ELSE 0 END) as 'noStage'
                                FROM campaignleads cl
                                INNER JOIN leads l ON l.id = cl.leadid
                                WHERE cl.campaignid = :campaignId
                                    AND l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}subskillid IS NOT NULL
                                    ${agentWhere}
                                GROUP BY cl.currentcampaignstageid, l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}SkillId, l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}subskillid WITH ROLLUP`, {
                                type: Sequelize.QueryTypes.SELECT,
                                replacements: query2Replacements
                            }))

                            //Query 5: Suppressions
                            var query3Replacements = {
                                campaignId: req.params.id
                            }
                            if (agentId) {
                                query3Replacements.agentId = parseInt(agentId)
                            }

                            // this is slightly different from original - we join campaign leads
                            promises.push(db.query(`
                                SELECT
                                    cl.currentCampaignStageId as 'stageId',
                                    l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}SkillId as 'reportingGroupId',
                                    l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}subskillid as 'skillId',
                                    COUNT(*) as 'suppressed'
                                FROM suppressions s
                                INNER JOIN leads l ON l.id = s.leadId
                                INNER JOIN campaignleads cl ON cl.leadid = l.id AND cl.campaignid = :campaignId
                                WHERE s.campaignId = :campaignId
                                    AND s.finished = FALSE
                                    AND s.actualStartDate IS NOT NULL
                                    AND l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}subskillid IS NOT NULL
                                    ${agentWhere}
                                GROUP BY cl.currentCampaignStageId, l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}SkillId, l.${campaign.campaigntype.name == 'Telefunding' ? 'tf' : 'tm'}subskillid WITH ROLLUP`, {
                                type: Sequelize.QueryTypes.SELECT,
                                replacements: query3Replacements
                            }))

                            Promise.all(promises)
                                .then(results => {
                                    // Validate query results
                                    if (!results || results.length !== 5) {
                                        throw new Error('Invalid query results: Expected 5 result sets')
                                    }

                                    // Validate that we have the required data structures
                                    if (!Array.isArray(results[0])) {
                                        throw new Error('Invalid call attempts data: Expected array')
                                    }
                                    if (!Array.isArray(results[1])) {
                                        throw new Error('Invalid call results data: Expected array')
                                    }
                                    if (!Array.isArray(results[2])) {
                                        throw new Error('Invalid campaign leads data: Expected array')
                                    }
                                    if (!Array.isArray(results[3])) {
                                        throw new Error('Invalid lead status data: Expected array')
                                    }
                                    if (!Array.isArray(results[4])) {
                                        throw new Error('Invalid suppressions data: Expected array')
                                    }

                                    var data = processStageViewV2Data(results[0], results[1], results[2], results[3], results[4], campaign, skills, subskills, rules)
                                    res.status(200).send(data)
                                })
                                .catch(err => {
                                    console.log(err)
                                    return res.status(500).send({
                                        error: err.message
                                    })
                                })
                        })
                })
        })
    })



    function processStageViewV2Data(callAttemptsData, callResultsData, campaignLeadsData, leadStatusData, suppressionsData, campaign, skills, subskills, rules) {
        // Input validation
        if (!campaign || !campaign.campaignstages) {
            throw new Error('Invalid campaign data: Missing campaign or campaign stages')
        }
        if (!Array.isArray(skills)) {
            throw new Error('Invalid skills data: Expected array')
        }
        if (!Array.isArray(subskills)) {
            throw new Error('Invalid subskills data: Expected array')
        }
        if (!Array.isArray(rules)) {
            throw new Error('Invalid rules data: Expected array')
        }

        var stages = []
        var totalAttempts = 0
        var totalLeads = 0
        var totalExhausted = 0
        var totalLeadsWithAttempts = 0
        var totalViable = 0
        var totalCallback = 0
        var totalDontContactUntil = 0
        var totalNoStage = 0
        var totalBadNumbers = 0
        var totalDialAttemptBuckets = {
            zero: 0,
            one: 0,
            twoToFour: 0,
            fiveToNineteen: 0,
            twentyPlus: 0
        }



        // Create lookup map
        var callResultsMap = {}
        callResultsData.forEach(row => {
            callResultsMap[row.leadid] = row.calls_made || 0
        })

        // Helper function to calculate dial count buckets from separated data
        function calculateDialCountBuckets(stageId, reportingGroupId, skillId) {
            var buckets = { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }

            campaignLeadsData.forEach(lead => {
                // Match the grouping criteria
                var matchesStage = !stageId || lead.stageId === stageId
                var matchesReportingGroup = !reportingGroupId || lead.reportingGroupId === reportingGroupId
                var matchesSkill = !skillId || lead.skillId === skillId

                if (matchesStage && matchesReportingGroup && matchesSkill) {
                    var callsMade = callResultsMap[lead.leadid] || 0

                    if (callsMade === 0) buckets.zero++
                    else if (callsMade === 1) buckets.one++
                    else if (callsMade >= 2 && callsMade <= 4) buckets.twoToFour++
                    else if (callsMade >= 5 && callsMade <= 19) buckets.fiveToNineteen++
                    else if (callsMade >= 20) buckets.twentyPlus++
                }
            })

            return buckets
        }

        // Process call attempts data
        callAttemptsData.forEach(function(row) {
            try {
                // Validate row data
                if (!row || typeof row !== 'object') {
                    console.warn('Invalid call attempts row: Expected object, got', typeof row)
                    return
                }

                // Validate numeric fields
                if (row.attempts && (isNaN(parseInt(row.attempts)) || parseInt(row.attempts) < 0)) {
                    console.warn('Invalid attempts value in call attempts row:', row.attempts)
                    return
                }
                if (row.leads && (isNaN(parseInt(row.leads)) || parseInt(row.leads) < 0)) {
                    console.warn('Invalid leads value in call attempts row:', row.leads)
                    return
                }

                // Validate ID references (only for non-ROLLUP rows)
                if (row.stageId && !_.findWhere(campaign.campaignstages, { id: row.stageId })) {
                    console.warn('Invalid stageId in call attempts row:', row.stageId)
                }
                if (row.reportingGroupId && !_.findWhere(skills, { id: row.reportingGroupId })) {
                    console.warn('Invalid reportingGroupId in call attempts row:', row.reportingGroupId)
                }
                if (row.skillId && !_.findWhere(subskills, { id: row.skillId })) {
                    console.warn('Invalid skillId in call attempts row:', row.skillId)
                }
                // ROLLUP Level 0: Grand total (all nulls)
                if (!row.stageId) {
                    totalAttempts = row.attempts
                    totalLeadsWithAttempts = row.leads
                    // Calculate total dial count buckets from separated data
                    totalDialAttemptBuckets = calculateDialCountBuckets(null, null, null)
                    return
                }

                // ROLLUP Level 1: Stage totals (reportingGroupId = null)
                if (!row.reportingGroupId) {
                    var stage = _.findWhere(campaign.campaignstages, {
                        id: row.stageId
                    })

                    var existingStage = _.findWhere(stages, {
                        id: row.stageId
                    })

                    if (existingStage) {
                        // Set stage totals from ROLLUP stage total row
                        existingStage.leadsWithAttempts = row.leads
                        existingStage.callAttempts = row.attempts
                        existingStage.dialAttemptBuckets = calculateDialCountBuckets(row.stageId, null, null)
                    } else {
                        // Create new stage with ROLLUP totals
                        var newstage = {
                            id: row.stageId,
                            name: stage ? stage.name : '',
                            leadsWithAttempts: row.leads,
                            callAttempts: row.attempts,
                            dialAttemptBuckets: calculateDialCountBuckets(row.stageId, null, null),
                            reportingGroups: []
                        }
                        stages.push(newstage)
                    }
                    return // Don't process as reporting group
                }

                // ROLLUP Level 2: Reporting group totals (skillId = null)
                if (!row.skillId) {
                    var existingStage = _.findWhere(stages, {
                        id: row.stageId
                    })

                    if (existingStage) {
                        var existingReportingGroup = _.findWhere(existingStage.reportingGroups, {
                            id: row.reportingGroupId
                        })

                        if (existingReportingGroup) {
                            // Update reporting group totals from ROLLUP reporting group total row
                            existingReportingGroup.leads = row.leads
                            existingReportingGroup.callAttempts = row.attempts
                            existingReportingGroup.dialAttemptBuckets = calculateDialCountBuckets(row.stageId, row.reportingGroupId, null)
                        } else {
                            // Create new reporting group with ROLLUP totals
                            var reportingGroupInfo = _.findWhere(skills, {
                                id: row.reportingGroupId
                            })

                            var newReportingGroup = {
                                id: row.reportingGroupId,
                                name: reportingGroupInfo ? reportingGroupInfo.name : 'Unknown Group',
                                leads: row.leads,
                                callAttempts: row.attempts,
                                dialAttemptBuckets: calculateDialCountBuckets(row.stageId, row.reportingGroupId, null),
                                leadTypes: []
                            }
                            if (!existingStage.reportingGroups) existingStage.reportingGroups = []
                            existingStage.reportingGroups.push(newReportingGroup)
                        }
                    }
                    return // Don't process as lead type
                }

                // ROLLUP Level 3: Lead type totals (ruleId = null)
                if (!row.ruleId) {
                    var existingStage = _.findWhere(stages, {
                        id: row.stageId
                    })

                    if (existingStage) {
                        var existingReportingGroup = _.findWhere(existingStage.reportingGroups, {
                            id: row.reportingGroupId
                        })

                        if (existingReportingGroup) {
                            var existingLeadType = _.findWhere(existingReportingGroup.leadTypes, {
                                id: row.skillId
                            })

                            if (existingLeadType) {
                                // Update lead type totals from ROLLUP lead type total row
                                existingLeadType.callAttempts = row.attempts
                                existingLeadType.leadsWithAttempts = row.leads
                                existingLeadType.dialAttemptBuckets = calculateDialCountBuckets(row.stageId, row.reportingGroupId, row.skillId)
                            } else {
                                // Create new lead type with ROLLUP totals
                                var reportingGroupInfo = _.findWhere(skills, { id: row.reportingGroupId })
                                var subskillInfo = _.findWhere(subskills, { id: row.skillId })
                                
                                var newLeadType = {
                                    id: row.skillId,
                                    name: subskillInfo ? subskillInfo.name : 'Unknown Lead Type',
                                    callAttempts: row.attempts,
                                    leadsWithAttempts: row.leads,
                                    dialAttemptBuckets: calculateDialCountBuckets(row.stageId, row.reportingGroupId, row.skillId)
                                }
                                existingReportingGroup.leadTypes.push(newLeadType)
                            }
                        }
                    }
                    return // Don't process as detail row
                }

                // ROLLUP Level 4: Detail rows (all fields populated including ruleId) - Process as lead type details
                var stage = _.findWhere(campaign.campaignstages, {
                    id: row.stageId
                })



                var existingStage = _.findWhere(stages, {
                    id: row.stageId
                })

                if (existingStage) {
                    // Process detail row - don't overwrite stage totals!
                    var existingReportingGroup = _.findWhere(existingStage.reportingGroups, {
                        id: row.reportingGroupId
                    })

                    if (!existingReportingGroup) {
                        // This shouldn't happen if ROLLUP data is processed in order
                        // But create reporting group if missing (defensive programming)
                        var reportingGroupInfo = _.findWhere(skills, {
                            id: row.reportingGroupId
                        })

                        existingReportingGroup = {
                            id: row.reportingGroupId,
                            name: reportingGroupInfo ? reportingGroupInfo.name : 'Unknown Group',
                            leads: 0, // Will be set by ROLLUP reporting group total row
                            callAttempts: 0, // Will be set by ROLLUP reporting group total row
                            dialAttemptBuckets: calculateDialCountBuckets(row.stageId, row.reportingGroupId, null),
                            leadTypes: []
                        }
                        if (!existingStage.reportingGroups) existingStage.reportingGroups = []
                        existingStage.reportingGroups.push(existingReportingGroup)
                    }

                    var existingLeadType = _.findWhere(existingReportingGroup.leadTypes, {
                        id: row.skillId
                    })

                    if (!existingLeadType) {
                        // skillId is the subskill ID - find the subskill info
                        var reportingGroupInfo = _.findWhere(skills, { id: row.reportingGroupId })
                        var subskillInfo = _.findWhere(subskills, { id: row.skillId })

                        existingLeadType = {
                            id: row.skillId,
                            name: subskillInfo ? subskillInfo.name : 'Unknown Lead Type',
                            callAttempts: row.attempts,
                            leadsWithAttempts: row.leads,
                            dialAttemptBuckets: calculateDialCountBuckets(row.stageId, row.reportingGroupId, row.skillId)
                        }
                        existingReportingGroup.leadTypes.push(existingLeadType)
                    } else {
                        // Don't overwrite ROLLUP totals with detail row data!
                        // Detail rows are for rule-level breakdown, but we aggregate at lead type level
                        // The ROLLUP lead type total (ruleId = null) should be the authoritative total
                        // Only update dial buckets which need to be recalculated from individual lead data
                        existingLeadType.dialAttemptBuckets = calculateDialCountBuckets(row.stageId, row.reportingGroupId, row.skillId)
                    }

                    // Dialing rule data is aggregated at lead type level, no separate dialing rules segmentation needed
                } else {
                    // This shouldn't happen if ROLLUP data is processed in order
                    // Stage should be created by stage total row first
                    // But create stage if missing (defensive programming)
                    var newstage = {
                        id: row.stageId,
                        name: stage ? stage.name : '',
                        leadsWithAttempts: 0, // Will be set by ROLLUP stage total row
                        callAttempts: 0, // Will be set by ROLLUP stage total row
                        dialAttemptBuckets: calculateDialCountBuckets(row.stageId, null, null),
                        reportingGroups: []
                    }

                    // Add reporting group and lead type for this detail row
                    var reportingGroupInfo = _.findWhere(skills, {
                        id: row.reportingGroupId
                    })

                    // skillId is the subskill ID
                    var subskillInfo = _.findWhere(subskills, { id: row.skillId })

                    var newReportingGroup = {
                        id: row.reportingGroupId,
                        name: reportingGroupInfo ? reportingGroupInfo.name : 'Unknown Group',
                        leads: 0, // Will be set by ROLLUP reporting group total row
                        callAttempts: 0, // Will be set by ROLLUP reporting group total row
                        dialAttemptBuckets: calculateDialCountBuckets(row.stageId, row.reportingGroupId, null),
                        leadTypes: [{
                            id: row.skillId,
                            name: subskillInfo ? subskillInfo.name : 'Unknown Lead Type',
                            callAttempts: row.attempts,
                            leadsWithAttempts: row.leads,
                            dialAttemptBuckets: calculateDialCountBuckets(row.stageId, row.reportingGroupId, row.skillId)
                        }]
                    }
                    newstage.reportingGroups.push(newReportingGroup)
                    stages.push(newstage)
                }
            } catch (err) {
                console.log(row)
                console.log(err)
            }
        })

        // Process lead status data
        leadStatusData.forEach(function(row) {
            try {
                // Validate row data
                if (!row || typeof row !== 'object') {
                    console.warn('Invalid lead status row: Expected object, got', typeof row)
                    return
                }

                // Validate numeric fields
                var numericFields = ['leads', 'exhausted', 'viable', 'callback', 'dontContactUntil', 'noStage', 'bad_numbers']
                for (var i = 0; i < numericFields.length; i++) {
                    var field = numericFields[i]
                    if (row[field] !== undefined && row[field] !== null && (isNaN(parseInt(row[field])) || parseInt(row[field]) < 0)) {
                        console.warn('Invalid ' + field + ' value in lead status row:', row[field])
                        return
                    }
                }
                // ROLLUP Level 0: Grand total (all nulls)
                if (!row.stageId && !row.reportingGroupId && !row.skillId) {
                    totalLeads = row.leads
                    totalExhausted = row.exhausted
                    totalViable = row.viable
                    totalCallback = row.callback
                    totalDontContactUntil = row.dontContactUntil
                    totalNoStage = row.noStage
                    totalBadNumbers = row.bad_numbers
                    return
                }

                // Skip intermediate ROLLUP levels that we don't need
                if (!row.stageId) {
                    return
                }

                var stage = _.findWhere(campaign.campaignstages, {
                    id: row.stageId
                })

                var existingStage = _.findWhere(stages, {
                    id: row.stageId
                })

                // ROLLUP Level 1: Stage totals (reportingGroupId = null, skillId = null)
                if (!row.reportingGroupId && !row.skillId) {
                    if (existingStage) {
                        // Set stage totals from ROLLUP stage total row
                        existingStage.leads = row.leads
                        existingStage.exhausted = row.exhausted
                        existingStage.viable = row.viable
                        existingStage.dontContactUntil = row.dontContactUntil
                        existingStage.callback = row.callback
                        existingStage.badNumber = row.bad_numbers
                    }
                    return // Don't process as reporting group
                }

                // ROLLUP Level 2: Reporting group totals (skillId = null)
                if (!row.skillId) {
                    if (existingStage && row.reportingGroupId) {
                        var existingReportingGroup = _.findWhere(existingStage.reportingGroups, {
                            id: row.reportingGroupId
                        })
                        if (existingReportingGroup) {
                            // Set reporting group totals from ROLLUP reporting group total row
                            existingReportingGroup.leads = row.leads
                            existingReportingGroup.exhausted = row.exhausted
                            existingReportingGroup.viable = row.viable
                            existingReportingGroup.dontContactUntil = row.dontContactUntil
                            existingReportingGroup.callback = row.callback
                            existingReportingGroup.badNumber = row.bad_numbers
                        }
                    }
                    return // Don't process as lead type
                }

                // ROLLUP Level 3: Lead type details (all fields populated)
                if (existingStage && row.reportingGroupId && row.skillId) {
                    var existingReportingGroup = _.findWhere(existingStage.reportingGroups, {
                        id: row.reportingGroupId
                    })
                    if (existingReportingGroup) {
                        // Don't overwrite reporting group totals with detail row data
                        // Reporting group totals are set by ROLLUP reporting group total rows above
                        var existingLeadType = _.findWhere(existingReportingGroup.leadTypes, {
                            id: row.skillId
                        })
                        if (existingLeadType) {
                            if (existingLeadType.leads === undefined) {
                                existingLeadType.leads = row.leads
                            }
                            existingLeadType.exhausted = row.exhausted
                            existingLeadType.viable = row.viable
                            existingLeadType.dontContactUntil = row.dontContactUntil
                            existingLeadType.callback = row.callback
                            existingLeadType.badNumber = row.bad_numbers
                        } else {
                            // Find the correct subskill info for lead type name
                            var reportingGroupInfo = _.findWhere(skills, { id: row.reportingGroupId })
                            var subskillInfo = _.findWhere(subskills, { id: row.skillId })

                            if (!existingReportingGroup.leadTypes) existingReportingGroup.leadTypes = []
                            existingReportingGroup.leadTypes.push({
                                id: row.skillId,
                                name: subskillInfo ? subskillInfo.name : 'Unknown Lead Type',
                                exhausted: row.exhausted,
                                leads: row.leads,
                                viable: row.viable,
                                dontContactUntil: row.dontContactUntil,
                                callback: row.callback,
                                badNumber: row.bad_numbers
                            })
                        }
                    } else {
                        // Create missing reporting group and lead type
                        var reportingGroupInfo = _.findWhere(skills, { id: row.reportingGroupId })
                        var subskillInfo = _.findWhere(subskills, { id: row.skillId })

                        var newReportingGroup = {
                            id: row.reportingGroupId,
                            name: reportingGroupInfo ? reportingGroupInfo.name : 'Unknown Group',
                            leads: row.leads,
                            exhausted: row.exhausted,
                            viable: row.viable,
                            dontContactUntil: row.dontContactUntil,
                            callback: row.callback,
                            badNumber: row.bad_numbers,
                            callAttempts: 0,
                            leadsWithAttempts: 0,
                            dialAttemptBuckets: calculateDialCountBuckets(row.stageId, row.reportingGroupId, null),
                            leadTypes: [{
                                id: row.skillId,
                                name: subskillInfo ? subskillInfo.name : 'Unknown Lead Type',
                                exhausted: row.exhausted,
                                leads: row.leads,
                                viable: row.viable,
                                dontContactUntil: row.dontContactUntil,
                                callback: row.callback,
                                badNumber: row.bad_numbers,
                                callAttempts: 0,
                                leadsWithAttempts: 0,
                                dialAttemptBuckets: { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }
                            }]
                        }

                        if (!existingStage.reportingGroups) existingStage.reportingGroups = []
                        existingStage.reportingGroups.push(newReportingGroup)
                    }
                } else {
                    var newStage = {
                        id: row.stageId,
                        name: stage ? stage.name : 'No stage found',
                        exhausted: row.exhausted,
                        leads: row.leads,
                        viable: row.viable,
                        dontContactUntil: row.dontContactUntil,
                        callback: row.callback,
                        badNumber: row.bad_numbers,
                        reportingGroups: []
                    }

                    if (row.reportingGroupId && row.skillId) {
                        // reportingGroupId is the skill ID
                        var reportingGroupInfo = _.findWhere(skills, {
                            id: row.reportingGroupId
                        })

                        // skillId is the subskill ID
                        var subskillInfo = _.findWhere(subskills, { id: row.skillId })

                        newStage.reportingGroups = [{
                            id: row.reportingGroupId,
                            name: reportingGroupInfo ? reportingGroupInfo.name : 'Unknown Group',
                            leads: row.leads,
                            exhausted: row.exhausted,
                            viable: row.viable,
                            dontContactUntil: row.dontContactUntil,
                            callback: row.callback,
                            badNumber: row.bad_numbers,
                            leadTypes: [{
                                id: row.skillId,
                                name: subskillInfo ? subskillInfo.name : 'Unknown Lead Type',
                                exhausted: row.exhausted,
                                leads: row.leads,
                                viable: row.viable,
                                dontContactUntil: row.dontContactUntil,
                                callback: row.callback,
                                badNumber: row.bad_numbers
                            }]
                        }]
                    }

                    stages.push(newStage)
                }
            } catch (err) {
                console.log(row)
                console.log(err)
            }
        })

        // Process suppressions data
        var suppressedTotal = 0
        suppressionsData.forEach(function(row) {
            try {
                // Validate row data
                if (!row || typeof row !== 'object') {
                    console.warn('Invalid suppressions row: Expected object, got', typeof row)
                    return
                }

                // Validate suppressed count
                if (row.suppressed !== undefined && row.suppressed !== null && (isNaN(parseInt(row.suppressed)) || parseInt(row.suppressed) < 0)) {
                    console.warn('Invalid suppressed value in suppressions row:', row.suppressed)
                    return
                }
                // ROLLUP Level 0: Grand total (all nulls)
                if (!row.stageId && !row.reportingGroupId && !row.skillId) {
                    suppressedTotal = parseInt(row.suppressed || 0)
                    return
                }

                // Skip ROLLUP intermediate levels for now, process only detail rows
                if (!row.stageId || !row.reportingGroupId || !row.skillId) {
                    return
                }

                var existingStage = _.findWhere(stages, {
                    id: row.stageId
                })
                if (existingStage) {
                    var existingReportingGroup = _.findWhere(existingStage.reportingGroups, {
                        id: row.reportingGroupId
                    })
                    if (existingReportingGroup) {
                        var existingLeadType = _.findWhere(existingReportingGroup.leadTypes, {
                            id: row.skillId
                        })
                        if (existingLeadType) {
                            existingLeadType.suppressed = parseInt(row.suppressed || 0)
                        } else {
                            // Create lead type if it doesn't exist (for suppressions only)
                            var subskillInfo = _.findWhere(subskills, { id: row.skillId })
                            var suppressedCount = parseInt(row.suppressed || 0)
                            existingReportingGroup.leadTypes.push({
                                id: row.skillId,
                                name: subskillInfo ? subskillInfo.name : 'Unknown Lead Type',
                                suppressed: suppressedCount,
                                callAttempts: 0,
                                leadsWithAttempts: 0,
                                leads: suppressedCount,  // Suppressed leads count as leads
                                exhausted: 0,
                                viable: 0,
                                dontContactUntil: 0,
                                callback: 0,
                                badNumber: 0,
                                dialAttemptBuckets: { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }
                            })
                        }
                    }
                }
            } catch (err) {
                console.log('Error processing suppression row:', row)
                console.log(err)
            }
        })

        // Calculate call attempts remaining for each lead type
        stages.forEach(function(stage) {
            stage.reportingGroups.forEach(function(reportingGroup) {
                reportingGroup.leadTypes.forEach(function(leadType) {
                    // Calculate call attempts remaining based on dialing rule quantities
                    var totalCallAttemptsRemaining = 0

                    // Find active dialing rules for this stage
                    var stageObj = _.findWhere(campaign.campaignstages, { id: stage.id })
                    if (stageObj && stageObj.campaignstagedatetimerules) {
                        stageObj.campaignstagedatetimerules.forEach(function(stageRule) {
                            var rule = _.findWhere(rules, { id: stageRule.datetimerulesetId })
                            if (rule) {
                                var currentDate = new Date()
                                var isActive = true

                                // Check if rule is active (same logic as dialing rules endpoint)
                                if (stageRule.startDate || stageRule.endDate) {
                                    if (stageRule.startDate && currentDate < new Date(stageRule.startDate)) {
                                        isActive = false
                                    }
                                    if (stageRule.endDate && currentDate > new Date(stageRule.endDate)) {
                                        isActive = false
                                    }
                                }

                                if (isActive) {
                                    var quantity = stageRule.quantity || 1
                                    var existingAttempts = leadType.callAttempts || 0
                                    var remaining = Math.max(0, (quantity * (leadType.leads || 0)) - existingAttempts)
                                    totalCallAttemptsRemaining += remaining

                                    // Call attempts remaining calculated at lead type level, no dialing rules segmentation needed
                                }
                            }
                        })
                    }

                    leadType.callAttemptsRemaining = totalCallAttemptsRemaining
                })
            })
        })

        // Sort lead types alphabetically within each reporting group
        stages.forEach(function(stage) {
            stage.reportingGroups.forEach(function(reportingGroup) {
                if (reportingGroup.leadTypes && reportingGroup.leadTypes.length > 0) {
                    reportingGroup.leadTypes.sort(function(a, b) {
                        var nameA = (a.name || '').toLowerCase()
                        var nameB = (b.name || '').toLowerCase()
                        return nameA.localeCompare(nameB)
                    })
                }
            })
        })

        return {
            stages: stages,
            totalAttempts: totalAttempts,
            totalCallAttempts: totalAttempts,  // Frontend expects this field name
            totalLeads: totalLeads,
            totalExhausted: totalExhausted,
            totalNoCallAttempts: totalExhausted,  // Frontend expects this field name
            totalLeadsWithAttempts: totalLeadsWithAttempts,
            totalViable: totalViable,
            totalCallback: totalCallback,
            totalDontContactUntil: totalDontContactUntil,
            totalNoStage: totalNoStage,
            totalBadNumbers: totalBadNumbers,
            suppressedTotal: suppressedTotal,
            totalDialAttemptBuckets: totalDialAttemptBuckets
        }
    }



}