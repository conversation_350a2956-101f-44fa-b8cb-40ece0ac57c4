const { expect } = require('chai')
const _ = require('underscore')

// Mock the processStageViewV2Data function for testing
// This would normally be imported from the controller
function processStageViewV2Data(callAttemptsData, callResultsData, campaignLeadsData, leadStatusData, suppressionsData, campaign, skills, rules) {
    var stages = []
    var totalAttempts = 0
    var totalLeads = 0
    var totalExhausted = 0
    var totalLeadsWithAttempts = 0
    var totalViable = 0
    var totalCallback = 0
    var totalDontContactUntil = 0
    var totalNoStage = 0
    var totalBadNumbers = 0
    var totalDialAttemptBuckets = {
        zero: 0,
        one: 0,
        twoToFour: 0,
        fiveToNineteen: 0,
        twentyPlus: 0
    }

    // Create fast lookup maps from the separated query results
    var callResultsMap = {}
    callResultsData.forEach(row => {
        callResultsMap[row.leadid] = row.calls_made || 0
    })

    // Helper function to calculate dial count buckets from separated data
    function calculateDialCountBuckets(stageId, reportingGroupId, skillId) {
        var buckets = { zero: 0, one: 0, twoToFour: 0, fiveToNineteen: 0, twentyPlus: 0 }
        
        campaignLeadsData.forEach(lead => {
            // Match the grouping criteria
            var matchesStage = !stageId || lead.stageId === stageId
            var matchesReportingGroup = !reportingGroupId || lead.reportingGroupId === reportingGroupId  
            var matchesSkill = !skillId || lead.skillId === skillId
            
            if (matchesStage && matchesReportingGroup && matchesSkill) {
                var callsMade = callResultsMap[lead.leadid] || 0
                
                if (callsMade === 0) buckets.zero++
                else if (callsMade === 1) buckets.one++
                else if (callsMade >= 2 && callsMade <= 4) buckets.twoToFour++
                else if (callsMade >= 5 && callsMade <= 19) buckets.fiveToNineteen++
                else if (callsMade >= 20) buckets.twentyPlus++
            }
        })
        
        return buckets
    }

    // Process call attempts data - simplified version for testing
    callAttemptsData.forEach(row => {
        try {
            if (!row.stageId) {
                totalAttempts = row.attempts
                totalLeadsWithAttempts = row.leads
                // Calculate total dial count buckets from separated data
                totalDialAttemptBuckets = calculateDialCountBuckets(null, null, null)
                return
            }

            var stage = _.findWhere(campaign.campaignstages, {
                id: row.stageId
            })

            var skill = _.findWhere(skills, {
                id: row.skillId
            })

            var existingStage = _.findWhere(stages, {
                id: row.stageId
            })

            if (existingStage) {
                existingStage.leadsWithAttempts = row.leads
                existingStage.callAttempts = row.attempts
            } else {
                // Create new stage
                var newstage = {
                    id: row.stageId,
                    name: stage ? stage.name : '',
                    leadsWithAttempts: row.leads,
                    callAttempts: row.attempts,
                    dialAttemptBuckets: calculateDialCountBuckets(row.stageId, null, null),
                    reportingGroups: []
                }
                stages.push(newstage)
            }
        } catch (err) {
            console.log('Error processing call attempts row:', err)
        }
    })

    return {
        stages: stages,
        totalAttempts: totalAttempts,
        totalLeads: totalLeads,
        totalLeadsWithAttempts: totalLeadsWithAttempts,
        totalDialAttemptBuckets: totalDialAttemptBuckets,
        totalExhausted: totalExhausted,
        totalViable: totalViable,
        totalCallback: totalCallback,
        totalDontContactUntil: totalDontContactUntil,
        totalNoStage: totalNoStage,
        totalBadNumbers: totalBadNumbers
    }
}

describe('processStageViewV2Data', function() {
    let mockCampaign, mockSkills, mockRules

    beforeEach(function() {
        mockCampaign = {
            campaignstages: [
                { id: 1, name: 'New Leads' },
                { id: 2, name: 'Follow Up' },
                { id: 3, name: 'Qualified' }
            ]
        }

        mockSkills = [
            { id: 101, name: 'Skill A' },
            { id: 102, name: 'Skill B' },
            { id: 201, name: 'Subskill A1' },
            { id: 202, name: 'Subskill A2' }
        ]

        mockRules = []
    })

    describe('Dial Count Bucket Calculations', function() {
        it('should correctly calculate dial count buckets for zero calls', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 100, leads: 50 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 50, leads: 25 }
            ]

            const callResultsData = [] // No call results = all zero dials

            const campaignLeadsData = [
                { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 3, stageId: 1, reportingGroupId: 101, skillId: 201 }
            ]

            const result = processStageViewV2Data(
                callAttemptsData, 
                callResultsData, 
                campaignLeadsData, 
                [], [], 
                mockCampaign, 
                mockSkills, 
                mockRules
            )

            expect(result.totalDialAttemptBuckets.zero).to.equal(3)
            expect(result.totalDialAttemptBuckets.one).to.equal(0)
            expect(result.totalDialAttemptBuckets.twoToFour).to.equal(0)
            expect(result.totalDialAttemptBuckets.fiveToNineteen).to.equal(0)
            expect(result.totalDialAttemptBuckets.twentyPlus).to.equal(0)
        })

        it('should correctly distribute leads across dial count buckets', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 200, leads: 100 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 100, leads: 50 }
            ]

            const callResultsData = [
                { leadid: 1, calls_made: 0 },   // zero bucket
                { leadid: 2, calls_made: 1 },   // one bucket  
                { leadid: 3, calls_made: 3 },   // twoToFour bucket
                { leadid: 4, calls_made: 10 },  // fiveToNineteen bucket
                { leadid: 5, calls_made: 25 }   // twentyPlus bucket
            ]

            const campaignLeadsData = [
                { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 3, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 4, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 5, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 6, stageId: 1, reportingGroupId: 101, skillId: 201 }  // No call results = zero
            ]

            const result = processStageViewV2Data(
                callAttemptsData, 
                callResultsData, 
                campaignLeadsData, 
                [], [], 
                mockCampaign, 
                mockSkills, 
                mockRules
            )

            expect(result.totalDialAttemptBuckets.zero).to.equal(2) // leadid 1 + leadid 6
            expect(result.totalDialAttemptBuckets.one).to.equal(1)  // leadid 2
            expect(result.totalDialAttemptBuckets.twoToFour).to.equal(1) // leadid 3
            expect(result.totalDialAttemptBuckets.fiveToNineteen).to.equal(1) // leadid 4
            expect(result.totalDialAttemptBuckets.twentyPlus).to.equal(1) // leadid 5

            // Total should equal number of campaign leads
            const totalBuckets = result.totalDialAttemptBuckets.zero + 
                               result.totalDialAttemptBuckets.one + 
                               result.totalDialAttemptBuckets.twoToFour + 
                               result.totalDialAttemptBuckets.fiveToNineteen + 
                               result.totalDialAttemptBuckets.twentyPlus
            expect(totalBuckets).to.equal(6)
        })
    })

    describe('Stage Processing', function() {
        it('should correctly process stages and calculate stage-level dial buckets', function() {
            const callAttemptsData = [
                { stageId: null, attempts: 300, leads: 150 }, // Total row
                { stageId: 1, reportingGroupId: 101, skillId: 201, attempts: 100, leads: 50 },
                { stageId: 2, reportingGroupId: 101, skillId: 201, attempts: 200, leads: 100 }
            ]

            const callResultsData = [
                { leadid: 1, calls_made: 1 },   // Stage 1 lead
                { leadid: 2, calls_made: 3 },   // Stage 1 lead
                { leadid: 3, calls_made: 5 },   // Stage 2 lead
                { leadid: 4, calls_made: 15 }   // Stage 2 lead
            ]

            const campaignLeadsData = [
                { leadid: 1, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 2, stageId: 1, reportingGroupId: 101, skillId: 201 },
                { leadid: 3, stageId: 2, reportingGroupId: 101, skillId: 201 },
                { leadid: 4, stageId: 2, reportingGroupId: 101, skillId: 201 }
            ]

            const result = processStageViewV2Data(
                callAttemptsData, 
                callResultsData, 
                campaignLeadsData, 
                [], [], 
                mockCampaign, 
                mockSkills, 
                mockRules
            )

            expect(result.stages).to.have.length(2)
            
            const stage1 = _.findWhere(result.stages, { id: 1 })
            const stage2 = _.findWhere(result.stages, { id: 2 })
            
            expect(stage1).to.exist
            expect(stage2).to.exist
            
            // Stage 1 should have: 1 lead with 1 call, 1 lead with 3 calls
            expect(stage1.dialAttemptBuckets.one).to.equal(1)
            expect(stage1.dialAttemptBuckets.twoToFour).to.equal(1)
            
            // Stage 2 should have: 1 lead with 5 calls, 1 lead with 15 calls  
            expect(stage2.dialAttemptBuckets.fiveToNineteen).to.equal(2)
        })
    })
})
