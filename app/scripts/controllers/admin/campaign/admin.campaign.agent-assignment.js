'use strict'

angular.module('dialerFrontendApp')
    .controller('CampaignAgentAssignmentCtrl', function ($scope, $q, Campaign, Agent, _, $timeout) {
        
        // Initialize scope variables
        $scope.loading = true
        $scope.currentView = 'stageLeadTypeAgent' // Default view
        $scope.hierarchicalData = {}
        $scope.selectedItems = []
        $scope.expandedNodes = {}
        $scope.selectAll = false
        $scope.saving = false
        
        // View configuration
        $scope.viewOptions = [
            {
                key: 'stageLeadTypeAgent',
                label: 'Stage → Lead Type → Agent',
                description: 'View agents organized by campaign stage and lead type'
            },
            {
                key: 'stageAgentLeadType',
                label: 'Stage → Agent → Lead Type',
                description: 'View lead types organized by campaign stage and agent'
            },
            {
                key: 'agentStageLeadType',
                label: 'Agent → Stage → Lead Type',
                description: 'View stages and lead types organized by agent'
            }
        ]
        
        // Load agent assignment data
        $scope.loadAgentAssignments = function () {
            if (!$scope.campaign || !$scope.campaign.id) {
                console.log('Campaign not available yet')
                return
            }

            $scope.loading = true

            Campaign.getAgentAssignments({ id: $scope.campaign.id })
                .$promise
                .then(function (data) {
                    console.log('Agent assignment data loaded:', data)
                    $scope.hierarchicalData = data.hierarchicalData
                    $scope.stages = data.stages
                    $scope.leadTypes = data.leadTypes
                    $scope.agents = data.agents
                    $scope.updateCurrentViewData()
                    $scope.loading = false
                })
                .catch(function (error) {
                    console.error('Error loading agent assignments:', error)
                    $scope.loading = false
                })
        }
        
        // Switch between different hierarchical views
        $scope.switchView = function (viewKey) {
            $scope.currentView = viewKey
            $scope.selectedItems = []
            $scope.expandedNodes = {}
            $scope.selectAll = false
            $scope.updateCurrentViewData()
        }
        
        // Get current view data - use a scope variable instead of function
        $scope.currentViewData = []

        $scope.updateCurrentViewData = function () {
            if (!$scope.hierarchicalData) {
                $scope.currentViewData = []
            } else {
                $scope.currentViewData = $scope.hierarchicalData[$scope.currentView] || []
            }
        }
        
        // Toggle node expansion
        $scope.toggleExpanded = function (node) {
            var nodeId = node.id + '_' + node.type
            $scope.expandedNodes[nodeId] = !$scope.expandedNodes[nodeId]
        }

        // Check if node is expanded
        $scope.isExpanded = function (nodeId) {
            return !!$scope.expandedNodes[nodeId]
        }
        
        // Toggle item selection
        $scope.toggleSelection = function (item) {
            var index = $scope.selectedItems.findIndex(function (selected) {
                return selected.id === item.id && 
                       selected.type === item.type &&
                       selected.stageId === item.stageId &&
                       selected.agentId === item.agentId &&
                       selected.leadTypeId === item.leadTypeId
            })
            
            if (index > -1) {
                $scope.selectedItems.splice(index, 1)
            } else {
                $scope.selectedItems.push(angular.copy(item))
            }
            
            $scope.updateSelectAllState()
        }
        
        // Check if item is selected
        $scope.isSelected = function (item) {
            return $scope.selectedItems.some(function (selected) {
                return selected.id === item.id && 
                       selected.type === item.type &&
                       selected.stageId === item.stageId &&
                       selected.agentId === item.agentId &&
                       selected.leadTypeId === item.leadTypeId
            })
        }
        
        // Toggle select all
        $scope.toggleSelectAll = function () {
            if ($scope.selectAll) {
                $scope.selectedItems = []
            } else {
                $scope.selectedItems = $scope.collectAllSelectableItems($scope.currentViewData)
            }
            $scope.selectAll = !$scope.selectAll
        }
        
        // Collect all selectable items from the current view
        $scope.collectAllSelectableItems = function (nodes) {
            var selectableItems = []

            function collectRecursive(nodeList) {
                nodeList.forEach(function (node) {
                    if (node.type === 'agent' && (node.stageId || node.agentId)) {
                        // This is a selectable agent assignment
                        selectableItems.push(angular.copy(node))
                    }

                    // Recursively collect from child nodes
                    if (node.agents && node.agents.length > 0) {
                        collectRecursive(node.agents)
                    }
                    if (node.leadTypes && node.leadTypes.length > 0) {
                        collectRecursive(node.leadTypes)
                    }
                    if (node.stages && node.stages.length > 0) {
                        collectRecursive(node.stages)
                    }
                })
            }

            collectRecursive(nodes)
            return selectableItems
        }
        
        // Update select all state based on current selections
        $scope.updateSelectAllState = function () {
            var allSelectableItems = $scope.collectAllSelectableItems($scope.currentViewData)
            $scope.selectAll = allSelectableItems.length > 0 &&
                              $scope.selectedItems.length === allSelectableItems.length
        }
        
        // Mass assign selected items
        $scope.massAssign = function () {
            if ($scope.selectedItems.length === 0) {
                alert('Please select items to assign')
                return
            }
            
            $scope.saving = true
            
            var assignments = $scope.selectedItems.map(function (item) {
                return {
                    stageId: item.stageId,
                    agentId: item.agentId || item.id,
                    leadTypeIds: item.leadTypeId ? [item.leadTypeId] : [],
                    action: 'assign'
                }
            })
            
            Campaign.updateAgentAssignments(
                { id: $scope.campaign.id },
                { assignments: assignments }
            )
                .$promise
                .then(function () {
                    $scope.selectedItems = []
                    $scope.selectAll = false
                    $scope.loadAgentAssignments()
                    $scope.saving = false
                })
                .catch(function (error) {
                    console.error('Error saving assignments:', error)
                    alert('Error saving assignments: ' + (error.data?.error || error.message))
                    $scope.saving = false
                })
        }
        
        // Mass unassign selected items
        $scope.massUnassign = function () {
            if ($scope.selectedItems.length === 0) {
                alert('Please select items to unassign')
                return
            }
            
            $scope.saving = true
            
            var assignments = $scope.selectedItems.map(function (item) {
                return {
                    stageId: item.stageId,
                    agentId: item.agentId || item.id,
                    action: 'unassign'
                }
            })
            
            Campaign.updateAgentAssignments(
                { id: $scope.campaign.id },
                { assignments: assignments }
            )
                .$promise
                .then(function () {
                    $scope.selectedItems = []
                    $scope.selectAll = false
                    $scope.loadAgentAssignments()
                    $scope.saving = false
                })
                .catch(function (error) {
                    console.error('Error saving assignments:', error)
                    alert('Error saving assignments: ' + (error.data?.error || error.message))
                    $scope.saving = false
                })
        }
        
        // Get assignment status text
        $scope.getAssignmentStatus = function (item) {
            if (item.assigned === true) {
                return 'Assigned'
            } else if (item.assigned === false) {
                return 'Not Assigned'
            }
            return ''
        }
        
        // Get assignment status class
        $scope.getAssignmentStatusClass = function (item) {
            if (item.assigned === true) {
                return 'text-success'
            } else if (item.assigned === false) {
                return 'text-muted'
            }
            return ''
        }
        
        // Initialize when campaign is available
        $scope.$watch('campaign.id', function (newVal) {
            console.log('Campaign ID changed:', newVal)
            if (newVal) {
                $scope.loadAgentAssignments()
            }
        })

        // Also try to load immediately if campaign is already available
        if ($scope.campaign && $scope.campaign.id) {
            $scope.loadAgentAssignments()
        }
    })
