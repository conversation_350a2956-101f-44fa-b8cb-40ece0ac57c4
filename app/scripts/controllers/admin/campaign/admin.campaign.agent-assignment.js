'use strict'

angular.module('dialerFrontendApp')
    .controller('CampaignAgentAssignmentCtrl', function ($scope, Campaign, _) {

        console.log('🔍 CampaignAgentAssignmentCtrl: Starting initialization')
        console.time('🔍 Controller Initialization')

        // Prevent multiple initialization
        if ($scope.agentAssignmentInitialized) {
            console.log('🔍 CampaignAgentAssignmentCtrl: Already initialized, skipping')
            return
        }
        $scope.agentAssignmentInitialized = true

        console.log('🔍 CampaignAgentAssignmentCtrl: Setting up scope variables')

        // Add performance monitoring
        window.agentAssignmentDebug = {
            startTime: Date.now(),
            checkpoints: []
        }

        function addCheckpoint(name) {
            var checkpoint = {
                name: name,
                time: Date.now(),
                elapsed: Date.now() - window.agentAssignmentDebug.startTime
            }
            window.agentAssignmentDebug.checkpoints.push(checkpoint)
            console.log('🔍 Checkpoint:', name, 'at', checkpoint.elapsed + 'ms')
        }

        addCheckpoint('Controller Start')

        // Initialize scope variables
        $scope.loading = false  // Start with false, will be set to true when loading starts
        $scope.currentView = 'stageLeadTypeAgent' // Default view
        $scope.hierarchicalData = {}
        $scope.selectedItems = []
        $scope.expandedNodes = {}
        $scope.selectAll = false
        $scope.saving = false
        $scope.assignmentConfig = {
            stageId: '',
            leadTypeId: ''
        }
        $scope.assignmentMatrix = []
        $scope.originalAssignments = {}
        $scope.showOnlyAssigned = false
        $scope.assignmentMode = 'stageAgent' // Default to Stage → Agent → Lead Types
        $scope.stageExpanded = {} // Track which stages are expanded
        $scope.leadTypeExpanded = {} // Track which lead types are expanded

        // Inline editing state
        $scope.addingAgentToStage = {} // Track which stages are in "add agent" mode
        $scope.addingAgentToLeadType = {} // Track which lead types are in "add agent" mode
        $scope.newAgentSearch = {} // Search terms for new agents
        $scope.newAgentSearchLeadType = {} // Search terms for new agents in lead type mode
        $scope.selectedNewAgent = {} // Selected agents for stages (legacy)
        $scope.selectedNewAgents = {} // Multi-selected agents for stages
        $scope.selectedNewAgentLeadType = {} // Selected agents for lead types (legacy)
        $scope.selectedNewAgentsLeadType = {} // Multi-selected agents for lead types
        $scope.newAgentLeadTypes = {} // Selected lead types for new agents
        $scope.showAgentDropdown = {} // Show/hide agent dropdowns
        $scope.showAgentDropdownLeadType = {} // Show/hide agent dropdowns for lead types
        $scope.leadTypeSearch = {} // Search terms for lead types
        $scope.showLeadTypeDropdown = {} // Show/hide lead type dropdowns

        // Inline lead type editing state
        $scope.editingAgentLeadTypes = {} // Track which agents are being edited
        $scope.editingLeadTypes = {} // Lead types being edited for agents
        $scope.editLeadTypeSearch = {} // Search terms for editing lead types
        $scope.showEditLeadTypeDropdown = {} // Show/hide edit lead type dropdowns

        // Third view: Agent → Stage → Lead Types state
        $scope.agentAssignments = [] // Agent-centric view data
        $scope.expandedAgents = {} // Track which agents are expanded

        // Bulk assignment state for third view
        $scope.selectedBulkAgents = {} // Selected agents for bulk assignment
        $scope.selectedBulkStages = {} // Selected stages for bulk assignment
        $scope.selectedBulkLeadTypes = {} // Selected lead types for bulk assignment
        $scope.bulkAgentSearch = '' // Search term for bulk agent selection
        $scope.bulkStageSearch = '' // Search term for bulk stage selection
        $scope.bulkLeadTypeSearch = '' // Search term for bulk lead type selection
        $scope.showBulkAgentDropdown = false // Show/hide bulk agent dropdown
        $scope.showBulkStageDropdown = false // Show/hide bulk stage dropdown
        $scope.showBulkLeadTypeDropdown = false // Show/hide bulk lead type dropdown

        // Agent-stage lead type editing state
        $scope.editingAgentStageLeadTypes = {} // Track which agent-stage combinations are being edited
        $scope.editAgentStageLeadTypeSearch = {} // Search terms for editing agent-stage lead types
        $scope.showEditAgentStageLeadTypeDropdown = {} // Show/hide edit agent-stage lead type dropdowns

        // Add agent to stages state
        $scope.addingAgentToStages = {} // Track which agents are being added to stages
        $scope.selectedAgentStages = {} // Selected stages for each agent
        $scope.selectedAgentLeadTypes = {} // Selected lead types for each agent
        $scope.newAgentStageSearch = {} // Search terms for agent stage selection
        $scope.newAgentLeadTypeSearch = {} // Search terms for agent lead type selection
        $scope.showAgentStageDropdown = {} // Show/hide agent stage dropdowns
        $scope.showAgentLeadTypeDropdown = {} // Show/hide agent lead type dropdowns
        $scope.stageAssignments = {} // For stage-only assignments
        $scope.originalStageAssignments = {}
        $scope.bulkSelection = {
            agents: {},
            stages: {},
            leadTypes: {}
        }

        // Add a simple test message
        $scope.testMessage = 'Agent Assignment Controller Loaded Successfully!'

        addCheckpoint('Scope Variables Set')

        console.timeEnd('🔍 Controller Initialization')

        // Expose debug function to global scope
        window.debugAgentAssignment = function() {
            console.log('🔍 Debug Info:')
            console.log('- Loading:', $scope.loading)
            console.log('- Current View:', $scope.currentView)
            console.log('- Hierarchical Data Keys:', Object.keys($scope.hierarchicalData))
            console.log('- Current View Data Length:', $scope.currentViewData ? $scope.currentViewData.length : 'undefined')
            console.log('- Campaign ID:', $scope.campaign ? $scope.campaign.id : 'undefined')
            console.log('- Performance:', window.agentAssignmentDebug)
            console.log('- Campaign Resource Available:', typeof Campaign)
        }

        // Add manual load function for testing
        window.forceLoadAgentAssignments = function() {
            console.log('🔍 Manual load triggered')
            $scope.loading = false
            $scope.$apply()
            $scope.loadAgentAssignments()
        }

        // Test the Campaign resource
        console.log('🔍 Testing Campaign resource:', typeof Campaign)
        if (typeof Campaign.getAgentAssignments === 'function') {
            console.log('🔍 getAgentAssignments method exists')
        } else {
            console.error('🔍 getAgentAssignments method NOT found!')
        }

        // Test a simple API call to verify the resource works
        window.testCampaignAPI = function() {
            console.log('🔍 Testing basic Campaign API call...')
            Campaign.get({ id: $scope.campaign.id })
                .$promise
                .then(function(data) {
                    console.log('🔍 Basic Campaign API call successful:', data)
                })
                .catch(function(error) {
                    console.error('🔍 Basic Campaign API call failed:', error)
                })
        }
        
        // No longer need view options - simplified to assignment modes only
        
        // Load agent assignment data
        $scope.loadAgentAssignments = function () {
            console.log('🔍 loadAgentAssignments: Function called')

            if (!$scope.campaign || !$scope.campaign.id) {
                console.log('🔍 loadAgentAssignments: Campaign not available yet')
                return
            }

            if ($scope.loading) {
                console.log('🔍 loadAgentAssignments: Already loading, skipping duplicate request')
                return
            }

            console.log('🔍 loadAgentAssignments: Starting load for campaign:', $scope.campaign.id)
            $scope.loading = true

            Campaign.getAgentAssignments({ id: $scope.campaign.id })
                .$promise
                .then(function (data) {
                    console.log('🔍 loadAgentAssignments: API response received, processing...')
                    console.log('🔍 loadAgentAssignments: Data keys:', Object.keys(data))

                    $scope.hierarchicalData = data.hierarchicalData || {}
                    $scope.stages = data.stages || []
                    $scope.leadTypes = data.leadTypes || []
                    $scope.agents = data.agents || []

                    console.log('🔍 loadAgentAssignments: Data assigned, updating view...')
                    $scope.updateCurrentViewData()
                    $scope.buildAssignmentMatrix()
                    $scope.buildStageAssignments()

                    // Invalidate cache when data loads
                    $scope.invalidateAssignedAgentsCache()



                    console.log('🔍 loadAgentAssignments: Setting loading to false')
                    $scope.loading = false

                    console.log('🔍 loadAgentAssignments: Load complete')
                })
                .catch(function (error) {
                    console.error('🔍 loadAgentAssignments: Error occurred:', error)
                    $scope.loading = false
                })
        }
        
        // No longer need view switching - simplified interface
        
        // Get current view data - use a scope variable instead of function
        $scope.currentViewData = []

        $scope.updateCurrentViewData = function () {
            addCheckpoint('updateCurrentViewData Start')
            console.log('🔍 updateCurrentViewData: Starting update for view:', $scope.currentView)

            if (!$scope.hierarchicalData) {
                console.log('🔍 updateCurrentViewData: No hierarchical data available')
                $scope.currentViewData = []
            } else {
                var data = $scope.hierarchicalData[$scope.currentView] || []
                console.log('🔍 updateCurrentViewData: Raw data length:', data.length)

                // Limit data size to prevent browser freezing
                if (data.length > 100) {
                    console.warn('🔍 updateCurrentViewData: Large dataset detected, limiting to first 100 items')
                    $scope.currentViewData = data.slice(0, 100)
                } else {
                    $scope.currentViewData = data
                }

                console.log('🔍 updateCurrentViewData: Final data length:', $scope.currentViewData.length)


            }

            addCheckpoint('updateCurrentViewData End')
        }



        // Make sure expand/collapse functions work for view-only pages
        $scope.toggleExpanded = function (node) {
            var nodeId = node.id + '_' + node.type
            console.log('🔍 toggleExpanded: nodeId =', nodeId, 'current state =', $scope.expandedNodes[nodeId])
            $scope.expandedNodes[nodeId] = !$scope.expandedNodes[nodeId]
            console.log('🔍 toggleExpanded: new state =', $scope.expandedNodes[nodeId])
        }

        $scope.isExpanded = function (nodeId) {
            var expanded = !!$scope.expandedNodes[nodeId]
            return expanded
        }


        

        
        // Simplified functions for view-only mode
        $scope.toggleSelection = function () {
            // No-op for view-only mode
        }

        $scope.isSelected = function () {
            // Always return false for view-only mode
            return false
        }
        
        // Toggle select all
        $scope.toggleSelectAll = function () {
            if ($scope.selectAll) {
                $scope.selectedItems = []
            } else {
                // For agent assignment, we want to select from the actual agents list, not the hierarchy
                if ($scope.currentView === 'agentStageLeadType') {
                    // In agent view, select from the top-level agents
                    $scope.selectedItems = $scope.agents.map(function(agent) {
                        return angular.copy(agent)
                    })
                } else {
                    // In other views, collect agents from the hierarchy
                    $scope.selectedItems = $scope.collectAllSelectableItems($scope.currentViewData)
                }
            }
            $scope.selectAll = !$scope.selectAll
            console.log('🔍 toggleSelectAll: Selected', $scope.selectedItems.length, 'items')
        }
        
        // Collect all selectable items from the current view (flat structure)
        $scope.collectAllSelectableItems = function (nodes) {
            var selectableItems = []
            var seenAgents = new Set() // Track unique agents to avoid duplicates

            function collectFromFlatStructure(nodeList) {
                nodeList.forEach(function (node) {
                    // Check if this node is selectable (agents)
                    if (node.type === 'agent') {
                        var agentKey = node.id + '_' + node.type
                        if (!seenAgents.has(agentKey)) {
                            seenAgents.add(agentKey)
                            selectableItems.push(angular.copy(node))
                        }
                    }

                    // Check children in flat structure
                    if (node.agents && node.agents.length > 0) {
                        node.agents.forEach(function(agent) {
                            if (agent.type === 'agent') {
                                var agentKey = agent.id + '_' + agent.type
                                if (!seenAgents.has(agentKey)) {
                                    seenAgents.add(agentKey)
                                    selectableItems.push(angular.copy(agent))
                                }
                            }
                        })
                    }
                    if (node.leadTypes && node.leadTypes.length > 0) {
                        node.leadTypes.forEach(function(leadType) {
                            if (leadType.agents && leadType.agents.length > 0) {
                                leadType.agents.forEach(function(agent) {
                                    if (agent.type === 'agent') {
                                        var agentKey = agent.id + '_' + agent.type
                                        if (!seenAgents.has(agentKey)) {
                                            seenAgents.add(agentKey)
                                            selectableItems.push(angular.copy(agent))
                                        }
                                    }
                                })
                            }
                        })
                    }
                    if (node.stages && node.stages.length > 0) {
                        collectFromFlatStructure(node.stages)
                    }
                })
            }

            collectFromFlatStructure(nodes)
            console.log('🔍 collectAllSelectableItems: Found', selectableItems.length, 'unique selectable items')
            console.log('🔍 collectAllSelectableItems: Agent names:', selectableItems.map(function(item) { return item.name }))
            return selectableItems
        }
        
        // Update select all state based on current selections
        $scope.updateSelectAllState = function () {
            var totalSelectableAgents = $scope.agents ? $scope.agents.length : 0
            $scope.selectAll = totalSelectableAgents > 0 &&
                              $scope.selectedItems.length === totalSelectableAgents
        }
        
        // Mass assign selected items
        $scope.massAssign = function () {
            if ($scope.selectedItems.length === 0) {
                alert('Please select items to assign')
                return
            }
            
            $scope.saving = true
            
            var assignments = $scope.selectedItems.map(function (item) {
                return {
                    stageId: item.stageId,
                    agentId: item.agentId || item.id,
                    leadTypeIds: item.leadTypeId ? [item.leadTypeId] : [],
                    action: 'assign'
                }
            })
            
            Campaign.updateAgentAssignments(
                { id: $scope.campaign.id },
                { assignments: assignments }
            )
                .$promise
                .then(function () {
                    $scope.selectedItems = []
                    $scope.selectAll = false
                    $scope.loadAgentAssignments()
                    $scope.saving = false
                })
                .catch(function (error) {
                    console.error('Error saving assignments:', error)
                    alert('Error saving assignments: ' + (error.data?.error || error.message))
                    $scope.saving = false
                })
        }
        
        // Mass unassign selected items
        $scope.massUnassign = function () {
            if ($scope.selectedItems.length === 0) {
                alert('Please select items to unassign')
                return
            }
            
            $scope.saving = true
            
            var assignments = $scope.selectedItems.map(function (item) {
                return {
                    stageId: item.stageId,
                    agentId: item.agentId || item.id,
                    action: 'unassign'
                }
            })
            
            Campaign.updateAgentAssignments(
                { id: $scope.campaign.id },
                { assignments: assignments }
            )
                .$promise
                .then(function () {
                    $scope.selectedItems = []
                    $scope.selectAll = false
                    $scope.loadAgentAssignments()
                    $scope.saving = false
                })
                .catch(function (error) {
                    console.error('Error saving assignments:', error)
                    alert('Error saving assignments: ' + (error.data?.error || error.message))
                    $scope.saving = false
                })
        }
        
        // Get assignment status text
        $scope.getAssignmentStatus = function (item) {
            if (item.assigned === true) {
                return 'Assigned'
            } else if (item.assigned === false) {
                return 'Not Assigned'
            }
            return ''
        }
        
        // Get assignment status class
        $scope.getAssignmentStatusClass = function (item) {
            if (item.assigned === true) {
                return 'text-success'
            } else if (item.assigned === false) {
                return 'text-muted'
            }
            return ''
        }

        // Open assignment configuration modal
        $scope.openAssignmentModal = function () {
            if ($scope.selectedItems.length === 0) {
                alert('Please select agents to assign')
                return
            }

            // Reset assignment config
            $scope.assignmentConfig = {
                stageId: '',
                leadTypeId: ''
            }

            // Mark all selected items as selected in modal
            $scope.selectedItems.forEach(function(item) {
                item.selected = true
            })

            $('#assignmentModal').modal('show')
        }

        // Execute the assignment
        $scope.executeAssignment = function () {
            if (!$scope.assignmentConfig.stageId || !$scope.assignmentConfig.leadTypeId) {
                alert('Please select both a stage and lead type')
                return
            }

            var agentsToAssign = $scope.selectedItems.filter(function(item) {
                return item.selected
            })

            if (agentsToAssign.length === 0) {
                alert('Please select at least one agent to assign')
                return
            }

            $scope.saving = true

            var assignments = agentsToAssign.map(function (agent) {
                return {
                    stageId: parseInt($scope.assignmentConfig.stageId),
                    agentId: agent.id,
                    leadTypeIds: [parseInt($scope.assignmentConfig.leadTypeId)],
                    action: 'assign'
                }
            })

            console.log('🔍 Creating assignments:', assignments)

            Campaign.updateAgentAssignments(
                { id: $scope.campaign.id },
                { assignments: assignments }
            )
                .$promise
                .then(function () {
                    $('#assignmentModal').modal('hide')
                    $scope.selectedItems = []
                    $scope.selectAll = false
                    $scope.loadAgentAssignments()
                    $scope.saving = false
                    alert('Assignments created successfully!')
                })
                .catch(function (error) {
                    console.error('Error creating assignments:', error)
                    alert('Error creating assignments: ' + (error.data?.error || error.message))
                    $scope.saving = false
                })
        }

        // Build assignment matrix for the new interface
        $scope.buildAssignmentMatrix = function () {
            console.log('🔍 buildAssignmentMatrix: Starting...')
            $scope.assignmentMatrix = []
            $scope.originalAssignments = {}

            if (!$scope.stages || !$scope.leadTypes || !$scope.agents) {
                console.log('🔍 buildAssignmentMatrix: Missing required data')
                return
            }

            // Create matrix entry for each stage/leadType combination
            $scope.stages.forEach(function(stage) {
                $scope.leadTypes.forEach(function(leadType) {
                    var matrixEntry = {
                        stageId: stage.id,
                        stageName: stage.name,
                        leadTypeId: leadType.id,
                        leadTypeName: leadType.name,
                        agents: {},
                        hasChanges: false,
                        hasAssignments: false,
                        allAssigned: false
                    }

                    // Initialize agent assignment status
                    $scope.agents.forEach(function(agent) {
                        var isAssigned = $scope.isAgentAssignedToStageLeadType(agent.id, stage.id, leadType.id)
                        matrixEntry.agents[agent.id] = {
                            assigned: isAssigned,
                            originallyAssigned: isAssigned,
                            changed: false
                        }

                        if (isAssigned) {
                            matrixEntry.hasAssignments = true
                        }
                    })

                    // Check if all agents are assigned
                    matrixEntry.allAssigned = $scope.agents.every(function(agent) {
                        return matrixEntry.agents[agent.id].assigned
                    })

                    $scope.assignmentMatrix.push(matrixEntry)

                    // Store original state for change tracking
                    var key = stage.id + '_' + leadType.id
                    $scope.originalAssignments[key] = angular.copy(matrixEntry.agents)
                })
            })

            console.log('🔍 buildAssignmentMatrix: Created', $scope.assignmentMatrix.length, 'matrix entries')
        }

        // Build stage-only assignments
        $scope.buildStageAssignments = function () {
            console.log('🔍 buildStageAssignments: Starting...')
            $scope.stageAssignments = {}
            $scope.originalStageAssignments = {}

            if (!$scope.stages || !$scope.agents) {
                console.log('🔍 buildStageAssignments: Missing required data')
                return
            }

            $scope.stages.forEach(function(stage) {
                $scope.stageAssignments[stage.id] = {
                    agents: {},
                    hasChanges: false,
                    hasAssignments: false,
                    allAssigned: false
                }

                // Initialize agent assignment status for this stage
                $scope.agents.forEach(function(agent) {
                    var isAssigned = $scope.isAgentAssignedToStageOnly(agent.id, stage.id)
                    $scope.stageAssignments[stage.id].agents[agent.id] = {
                        assigned: isAssigned,
                        originallyAssigned: isAssigned,
                        changed: false
                    }

                    if (isAssigned) {
                        $scope.stageAssignments[stage.id].hasAssignments = true
                    }
                })

                // Check if all agents are assigned to this stage
                $scope.stageAssignments[stage.id].allAssigned = $scope.agents.every(function(agent) {
                    return $scope.stageAssignments[stage.id].agents[agent.id].assigned
                })

                // Store original state
                $scope.originalStageAssignments[stage.id] = angular.copy($scope.stageAssignments[stage.id].agents)
            })

            console.log('🔍 buildStageAssignments: Created assignments for', $scope.stages.length, 'stages')
        }

        // Check if an agent is assigned to a stage (all lead types)
        $scope.isAgentAssignedToStageOnly = function (agentId, stageId) {
            // This would check if the agent is assigned to ALL lead types for this stage
            // For now, we'll check if they're assigned to any lead type for this stage
            if (!$scope.hierarchicalData || !$scope.hierarchicalData.stageLeadTypeAgent) {
                return false
            }

            var stage = $scope.hierarchicalData.stageLeadTypeAgent.find(function(s) {
                return s.id === stageId
            })

            if (!stage || !stage.leadTypes) {
                return false
            }

            // Check if agent is assigned to ALL lead types for this stage
            var totalLeadTypes = stage.leadTypes.length
            var assignedLeadTypes = 0

            stage.leadTypes.forEach(function(leadType) {
                if (leadType.agents) {
                    var agent = leadType.agents.find(function(a) {
                        return a.id === agentId && a.assigned === true
                    })
                    if (agent) {
                        assignedLeadTypes++
                    }
                }
            })

            // Agent is considered "stage-only assigned" if they're assigned to ALL lead types
            return assignedLeadTypes === totalLeadTypes && totalLeadTypes > 0
        }

        // Check if an agent is currently assigned to a stage/leadType combination
        $scope.isAgentAssignedToStageLeadType = function (agentId, stageId, leadTypeId) {
            // Check in the hierarchical data for existing assignments
            if (!$scope.hierarchicalData || !$scope.hierarchicalData.stageLeadTypeAgent) {
                return false
            }

            var stage = $scope.hierarchicalData.stageLeadTypeAgent.find(function(s) {
                return s.id === stageId
            })

            if (!stage || !stage.leadTypes) {
                return false
            }

            var leadType = stage.leadTypes.find(function(lt) {
                return lt.id === leadTypeId
            })

            if (!leadType || !leadType.agents) {
                return false
            }

            var agent = leadType.agents.find(function(a) {
                return a.id === agentId
            })

            return agent && agent.assigned === true
        }

        // Mark assignment as changed
        $scope.markAssignmentChanged = function (assignment, agentId) {
            var originalKey = assignment.stageId + '_' + assignment.leadTypeId
            var originalState = $scope.originalAssignments[originalKey][agentId]

            assignment.agents[agentId].changed =
                assignment.agents[agentId].assigned !== originalState.originallyAssigned

            // Update assignment flags
            assignment.hasChanges = Object.keys(assignment.agents).some(function(id) {
                return assignment.agents[id].changed
            })

            assignment.hasAssignments = Object.keys(assignment.agents).some(function(id) {
                return assignment.agents[id].assigned
            })

            assignment.allAssigned = $scope.agents.every(function(agent) {
                return assignment.agents[agent.id].assigned
            })

            console.log('🔍 markAssignmentChanged: Assignment changed for', assignment.stageName, assignment.leadTypeName)
        }

        // Check if there are any changes to save
        $scope.hasChanges = function () {
            return $scope.assignmentMatrix.some(function(assignment) {
                return assignment.hasChanges
            })
        }

        // Toggle assignment mode (show all vs show only assigned)
        $scope.toggleAssignmentMode = function () {
            $scope.showOnlyAssigned = !$scope.showOnlyAssigned
        }

        // Reset all changes
        $scope.resetChanges = function () {
            $scope.assignmentMatrix.forEach(function(assignment) {
                Object.keys(assignment.agents).forEach(function(agentId) {
                    assignment.agents[agentId].assigned = assignment.agents[agentId].originallyAssigned
                    assignment.agents[agentId].changed = false
                })
                assignment.hasChanges = false
            })
        }

        // Save all assignment changes
        $scope.saveAllAssignments = function () {
            if (!$scope.hasChanges()) {
                alert('No changes to save')
                return
            }

            $scope.saving = true
            var assignments = []

            $scope.assignmentMatrix.forEach(function(assignment) {
                if (assignment.hasChanges) {
                    Object.keys(assignment.agents).forEach(function(agentId) {
                        var agentAssignment = assignment.agents[agentId]
                        if (agentAssignment.changed) {
                            assignments.push({
                                stageId: assignment.stageId,
                                agentId: parseInt(agentId),
                                leadTypeIds: [assignment.leadTypeId],
                                action: agentAssignment.assigned ? 'assign' : 'unassign'
                            })
                        }
                    })
                }
            })

            console.log('🔍 saveAllAssignments: Saving', assignments.length, 'assignment changes')

            Campaign.updateAgentAssignments(
                { id: $scope.campaign.id },
                { assignments: assignments }
            )
                .$promise
                .then(function () {
                    $scope.loadAgentAssignments() // Reload to get fresh data
                    $scope.saving = false
                    alert('All assignments saved successfully!')
                })
                .catch(function (error) {
                    console.error('Error saving assignments:', error)
                    alert('Error saving assignments: ' + (error.data?.error || error.message))
                    $scope.saving = false
                })
        }

        // Row actions
        $scope.assignAllAgentsToRow = function (assignment) {
            $scope.agents.forEach(function(agent) {
                assignment.agents[agent.id].assigned = true
                $scope.markAssignmentChanged(assignment, agent.id)
            })
        }

        $scope.unassignAllAgentsFromRow = function (assignment) {
            $scope.agents.forEach(function(agent) {
                assignment.agents[agent.id].assigned = false
                $scope.markAssignmentChanged(assignment, agent.id)
            })
        }

        // Bulk actions
        $scope.executeBulkAssign = function () {
            var selectedAgents = Object.keys($scope.bulkSelection.agents).filter(function(id) {
                return $scope.bulkSelection.agents[id]
            })
            var selectedStages = Object.keys($scope.bulkSelection.stages).filter(function(id) {
                return $scope.bulkSelection.stages[id]
            })
            var selectedLeadTypes = Object.keys($scope.bulkSelection.leadTypes).filter(function(id) {
                return $scope.bulkSelection.leadTypes[id]
            })

            if (selectedAgents.length === 0) {
                alert('Please select at least one agent')
                return
            }

            if (selectedStages.length === 0) {
                alert('Please select at least one stage')
                return
            }

            // If no lead types selected, use all lead types
            if (selectedLeadTypes.length === 0) {
                selectedLeadTypes = $scope.leadTypes.map(function(lt) { return lt.id.toString() })
            }

            var changesCount = 0
            selectedStages.forEach(function(stageId) {
                selectedLeadTypes.forEach(function(leadTypeId) {
                    var assignment = $scope.assignmentMatrix.find(function(a) {
                        return a.stageId.toString() === stageId && a.leadTypeId.toString() === leadTypeId
                    })

                    if (assignment) {
                        selectedAgents.forEach(function(agentId) {
                            if (!assignment.agents[agentId].assigned) {
                                assignment.agents[agentId].assigned = true
                                $scope.markAssignmentChanged(assignment, agentId)
                                changesCount++
                            }
                        })
                    }
                })
            })

            alert('Bulk assigned ' + changesCount + ' agent-stage-leadtype combinations')
        }

        $scope.executeBulkUnassign = function () {
            var selectedAgents = Object.keys($scope.bulkSelection.agents).filter(function(id) {
                return $scope.bulkSelection.agents[id]
            })
            var selectedStages = Object.keys($scope.bulkSelection.stages).filter(function(id) {
                return $scope.bulkSelection.stages[id]
            })
            var selectedLeadTypes = Object.keys($scope.bulkSelection.leadTypes).filter(function(id) {
                return $scope.bulkSelection.leadTypes[id]
            })

            if (selectedAgents.length === 0) {
                alert('Please select at least one agent')
                return
            }

            if (selectedStages.length === 0) {
                alert('Please select at least one stage')
                return
            }

            // If no lead types selected, use all lead types
            if (selectedLeadTypes.length === 0) {
                selectedLeadTypes = $scope.leadTypes.map(function(lt) { return lt.id.toString() })
            }

            var changesCount = 0
            selectedStages.forEach(function(stageId) {
                selectedLeadTypes.forEach(function(leadTypeId) {
                    var assignment = $scope.assignmentMatrix.find(function(a) {
                        return a.stageId.toString() === stageId && a.leadTypeId.toString() === leadTypeId
                    })

                    if (assignment) {
                        selectedAgents.forEach(function(agentId) {
                            if (assignment.agents[agentId].assigned) {
                                assignment.agents[agentId].assigned = false
                                $scope.markAssignmentChanged(assignment, agentId)
                                changesCount++
                            }
                        })
                    }
                })
            })

            alert('Bulk unassigned ' + changesCount + ' agent-stage-leadtype combinations')
        }

        // Tree expansion functions
        $scope.toggleStageExpanded = function (stageId) {
            $scope.stageExpanded[stageId] = !$scope.stageExpanded[stageId]
        }

        $scope.isStageExpanded = function (stageId) {
            return !!$scope.stageExpanded[stageId]
        }

        $scope.toggleLeadTypeExpanded = function (stageId, leadTypeId) {
            var key = stageId + '_' + leadTypeId
            $scope.leadTypeExpanded[key] = !$scope.leadTypeExpanded[key]
        }

        $scope.isLeadTypeExpanded = function (stageId, leadTypeId) {
            var key = stageId + '_' + leadTypeId
            return !!$scope.leadTypeExpanded[key]
        }

        // Helper functions for tree display
        $scope.getAssignedAgentsCount = function (stageId) {
            if (!$scope.stageAssignments[stageId]) return 0
            return Object.keys($scope.stageAssignments[stageId].agents).filter(function(agentId) {
                return $scope.stageAssignments[stageId].agents[agentId].assigned
            }).length
        }

        $scope.getStageSpecificAssignmentsCount = function (stageId) {
            return $scope.assignmentMatrix.filter(function(assignment) {
                return assignment.stageId === stageId && assignment.hasAssignments
            }).length
        }

        $scope.getAssignedAgentsCountForStageLeadType = function (stageId, leadTypeId) {
            var assignment = $scope.getAssignmentForStageLeadType(stageId, leadTypeId)
            if (!assignment) return 0
            return Object.keys(assignment.agents).filter(function(agentId) {
                return assignment.agents[agentId].assigned
            }).length
        }

        $scope.getAssignmentForStageLeadType = function (stageId, leadTypeId) {
            return $scope.assignmentMatrix.find(function(assignment) {
                return assignment.stageId === stageId && assignment.leadTypeId === leadTypeId
            })
        }

        $scope.markSpecificAssignmentChanged = function (stageId, leadTypeId, agentId) {
            var assignment = $scope.getAssignmentForStageLeadType(stageId, leadTypeId)
            if (assignment) {
                $scope.markAssignmentChanged(assignment, agentId)
            }
        }

        $scope.assignAllAgentsToStageLeadType = function (stageId, leadTypeId) {
            var assignment = $scope.getAssignmentForStageLeadType(stageId, leadTypeId)
            if (assignment) {
                $scope.assignAllAgentsToRow(assignment)
            }
        }

        $scope.unassignAllAgentsFromStageLeadType = function (stageId, leadTypeId) {
            var assignment = $scope.getAssignmentForStageLeadType(stageId, leadTypeId)
            if (assignment) {
                $scope.unassignAllAgentsFromRow(assignment)
            }
        }

        // New functions for the redesigned interface

        // Cache for assigned agents to prevent infinite digest
        $scope.assignedAgentsCache = {}
        $scope.assignedAgentsCacheVersion = 0

        // Function to invalidate cache when data changes
        $scope.invalidateAssignedAgentsCache = function () {
            $scope.assignedAgentsCacheVersion++
            $scope.assignedAgentsCache = {}
            $scope.computeAssignedAgentsData()
            console.log('🔍 Cache invalidated, version:', $scope.assignedAgentsCacheVersion)
        }

        // Compute assigned agents data once and store it
        $scope.stageAssignedAgents = {}
        $scope.stageLeadTypeAssignedAgents = {}

        $scope.computeAssignedAgentsData = function () {
            $scope.stageAssignedAgents = {}
            $scope.stageLeadTypeAssignedAgents = {}

            if (!$scope.hierarchicalData || !$scope.hierarchicalData.stageLeadTypeAgent || !$scope.stages) {
                return
            }

            // Compute assigned agents for each stage
            $scope.stages.forEach(function(stage) {
                var stageId = stage.id
                var stageData = $scope.hierarchicalData.stageLeadTypeAgent.find(function(s) {
                    return s.id === stageId
                })

                if (!stageData || !stageData.leadTypes) {
                    $scope.stageAssignedAgents[stageId] = []
                    return
                }

                var agentMap = {}

                // Collect all agents assigned to any lead type in this stage
                stageData.leadTypes.forEach(function(leadType) {
                    if (leadType.agents) {
                        leadType.agents.forEach(function(agent) {
                            if (agent.assigned) {
                                if (!agentMap[agent.id]) {
                                    agentMap[agent.id] = {
                                        id: agent.id,
                                        name: agent.name,
                                        assignedLeadTypes: []
                                    }
                                }
                                agentMap[agent.id].assignedLeadTypes.push({
                                    id: leadType.id,
                                    name: leadType.name
                                })
                            }
                        })
                    }

                    // Also compute for stage/lead type combinations
                    var key = stageId + '_' + leadType.id
                    $scope.stageLeadTypeAssignedAgents[key] = []

                    if (leadType.agents) {
                        leadType.agents.forEach(function(agent) {
                            if (agent.assigned) {
                                $scope.stageLeadTypeAssignedAgents[key].push({
                                    id: agent.id,
                                    name: agent.name
                                })
                            }
                        })
                    }
                })

                $scope.stageAssignedAgents[stageId] = Object.values(agentMap)
            })

            console.log('🔍 Computed assigned agents data')
        }

        // These functions removed - using computed properties instead to prevent infinite digest

        // Get count of lead type assignments for a stage
        $scope.getStageLeadTypeAssignmentsCount = function (stageId) {
            var count = 0

            if (!$scope.leadTypes) {
                return count
            }

            $scope.leadTypes.forEach(function(leadType) {
                var key = stageId + '_' + leadType.id
                if ($scope.stageLeadTypeAssignedAgents[key] && $scope.stageLeadTypeAssignedAgents[key].length > 0) {
                    count++
                }
            })

            return count
        }

        // Inline editing functions

        // Stage → Agent → Lead Types mode functions
        $scope.startAddingAgentToStage = function (stageId) {
            $scope.addingAgentToStage[stageId] = true
            $scope.newAgentSearch[stageId] = ''
            $scope.selectedNewAgent[stageId] = null
            $scope.newAgentLeadTypes[stageId] = {}
            $scope.showAgentDropdown[stageId] = false
            $scope.leadTypeSearch[stageId] = ''
            $scope.showLeadTypeDropdown[stageId] = false
        }

        $scope.cancelAddingAgentToStage = function (stageId) {
            $scope.addingAgentToStage[stageId] = false
            $scope.newAgentSearch[stageId] = ''
            $scope.selectedNewAgent[stageId] = null
            $scope.newAgentLeadTypes[stageId] = {}
            $scope.showAgentDropdown[stageId] = false
            $scope.leadTypeSearch[stageId] = ''
            $scope.showLeadTypeDropdown[stageId] = false
        }

        $scope.selectNewAgent = function (stageId, agent) {
            $scope.selectedNewAgent[stageId] = agent
            $scope.newAgentSearch[stageId] = agent.name
            $scope.showAgentDropdown[stageId] = false
        }

        $scope.getSelectedLeadTypesCountForStage = function (stageId) {
            if (!$scope.newAgentLeadTypes[stageId]) return 0
            return Object.keys($scope.newAgentLeadTypes[stageId]).filter(function(key) {
                return $scope.newAgentLeadTypes[stageId][key]
            }).length
        }

        // Lead type multi-select functions with reliable toggle
        $scope.toggleLeadTypeSelection = function (stageId, leadType) {
            // Ensure the object exists
            if (!$scope.newAgentLeadTypes[stageId]) {
                $scope.newAgentLeadTypes[stageId] = {}
            }

            // Reliable toggle - convert to boolean first
            var currentValue = !!$scope.newAgentLeadTypes[stageId][leadType.id]
            $scope.newAgentLeadTypes[stageId][leadType.id] = !currentValue

            console.log('Toggled lead type', leadType.name, 'for stage', stageId, 'from', currentValue, 'to', !currentValue)
        }

        $scope.removeLeadTypeSelection = function (stageId, leadTypeId) {
            if ($scope.newAgentLeadTypes[stageId]) {
                $scope.newAgentLeadTypes[stageId][leadTypeId] = false
            }
        }

        // Multi-select agent functions
        $scope.toggleAgentSelection = function (stageId, agent) {
            if (!$scope.selectedNewAgents[stageId]) {
                $scope.selectedNewAgents[stageId] = {}
            }

            var currentValue = !!$scope.selectedNewAgents[stageId][agent.id]
            $scope.selectedNewAgents[stageId][agent.id] = !currentValue

            console.log('Toggled agent', agent.name, 'for stage', stageId, 'from', currentValue, 'to', !currentValue)
        }

        $scope.removeAgentSelection = function (stageId, agentId) {
            if ($scope.selectedNewAgents[stageId]) {
                $scope.selectedNewAgents[stageId][agentId] = false
            }
        }

        $scope.getSelectedAgentsDisplay = function (stageId) {
            if (!$scope.selectedNewAgents[stageId] || !$scope.agents) return []

            return $scope.agents.filter(function(agent) {
                return $scope.selectedNewAgents[stageId][agent.id]
            })
        }

        $scope.getAvailableAgents = function (stageId) {
            if (!$scope.agents) return []

            // Filter out agents already assigned to this stage
            var assignedAgentIds = {}
            if ($scope.stageAssignedAgents[stageId]) {
                $scope.stageAssignedAgents[stageId].forEach(function(agent) {
                    assignedAgentIds[agent.id] = true
                })
            }

            return $scope.agents.filter(function(agent) {
                return !assignedAgentIds[agent.id]
            })
        }

        $scope.addAgentsToStageInline = function (stageId) {
            var selectedAgents = $scope.getSelectedAgentsDisplay(stageId)
            var selectedLeadTypes = $scope.getSelectedLeadTypesForStageDisplay(stageId)

            console.log('Adding agents to stage:', {
                stageId: stageId,
                agents: selectedAgents,
                leadTypes: selectedLeadTypes
            })

            // TODO: Implement actual save logic
            // For now, just clear the selections
            $scope.selectedNewAgents[stageId] = {}
            $scope.newAgentLeadTypes[stageId] = {}
            $scope.newAgentSearch[stageId] = ''
            $scope.addingAgentToStage[stageId] = false

            // Refresh data
            $scope.invalidateAssignedAgentsCache()
        }

        // Multi-select agent functions for lead type mode
        $scope.toggleAgentSelectionLeadType = function (stageId, leadTypeId, agent) {
            var key = stageId + '_' + leadTypeId
            if (!$scope.selectedNewAgentsLeadType[key]) {
                $scope.selectedNewAgentsLeadType[key] = {}
            }

            var currentValue = !!$scope.selectedNewAgentsLeadType[key][agent.id]
            $scope.selectedNewAgentsLeadType[key][agent.id] = !currentValue

            console.log('Toggled agent', agent.name, 'for lead type', leadTypeId, 'in stage', stageId, 'from', currentValue, 'to', !currentValue)
        }

        $scope.removeAgentSelectionLeadType = function (stageId, leadTypeId, agentId) {
            var key = stageId + '_' + leadTypeId
            if ($scope.selectedNewAgentsLeadType[key]) {
                $scope.selectedNewAgentsLeadType[key][agentId] = false
            }
        }

        $scope.getSelectedAgentsLeadTypeDisplay = function (stageId, leadTypeId) {
            var key = stageId + '_' + leadTypeId
            if (!$scope.selectedNewAgentsLeadType[key] || !$scope.agents) return []

            return $scope.agents.filter(function(agent) {
                return $scope.selectedNewAgentsLeadType[key][agent.id]
            })
        }

        $scope.getAvailableAgentsForLeadType = function (stageId, leadTypeId) {
            if (!$scope.agents) return []

            // Filter out agents already assigned to this stage + lead type combination
            var key = stageId + '_' + leadTypeId
            var assignedAgentIds = {}
            if ($scope.stageLeadTypeAssignedAgents[key]) {
                $scope.stageLeadTypeAssignedAgents[key].forEach(function(agent) {
                    assignedAgentIds[agent.id] = true
                })
            }

            return $scope.agents.filter(function(agent) {
                return !assignedAgentIds[agent.id]
            })
        }

        $scope.addAgentsToLeadTypeInline = function (stageId, leadTypeId) {
            var key = stageId + '_' + leadTypeId
            var selectedAgents = $scope.getSelectedAgentsLeadTypeDisplay(stageId, leadTypeId)

            console.log('Adding agents to lead type:', {
                stageId: stageId,
                leadTypeId: leadTypeId,
                agents: selectedAgents
            })

            // TODO: Implement actual save logic
            // For now, just clear the selections
            $scope.selectedNewAgentsLeadType[key] = {}
            $scope.newAgentSearchLeadType[key] = ''
            $scope.addingAgentToLeadType[key] = false

            // Refresh data
            $scope.invalidateAssignedAgentsCache()
        }

        $scope.getSelectedLeadTypesForStageDisplay = function (stageId) {
            if (!$scope.newAgentLeadTypes[stageId] || !$scope.leadTypes) return []

            return $scope.leadTypes.filter(function(leadType) {
                return $scope.newAgentLeadTypes[stageId][leadType.id]
            })
        }



        $scope.addAgentToStageInline = function (stageId) {
            var agent = $scope.selectedNewAgent[stageId]
            var selectedLeadTypes = $scope.newAgentLeadTypes[stageId]

            console.log('Adding agent to stage inline:', {
                stageId: stageId,
                agent: agent,
                leadTypes: selectedLeadTypes
            })

            // TODO: Implement actual assignment logic
            // For now, just cancel the form
            $scope.cancelAddingAgentToStage(stageId)

            // Refresh data
            $scope.invalidateAssignedAgentsCache()
        }

        // Stage → Lead Type → Agents mode functions
        $scope.startAddingAgentToLeadType = function (stageId, leadTypeId) {
            var key = stageId + '_' + leadTypeId
            $scope.addingAgentToLeadType[key] = true
            $scope.newAgentSearchLeadType[key] = ''
            $scope.selectedNewAgentLeadType[key] = null
            $scope.showAgentDropdownLeadType[key] = false
        }

        $scope.cancelAddingAgentToLeadType = function (stageId, leadTypeId) {
            var key = stageId + '_' + leadTypeId
            $scope.addingAgentToLeadType[key] = false
            $scope.newAgentSearchLeadType[key] = ''
            $scope.selectedNewAgentLeadType[key] = null
            $scope.showAgentDropdownLeadType[key] = false
        }

        $scope.selectNewAgentForLeadType = function (stageId, leadTypeId, agent) {
            var key = stageId + '_' + leadTypeId
            $scope.selectedNewAgentLeadType[key] = agent
            $scope.newAgentSearchLeadType[key] = agent.name
            $scope.showAgentDropdownLeadType[key] = false
        }

        $scope.addAgentToLeadTypeInline = function (stageId, leadTypeId) {
            var key = stageId + '_' + leadTypeId
            var agent = $scope.selectedNewAgentLeadType[key]

            console.log('Adding agent to lead type inline:', {
                stageId: stageId,
                leadTypeId: leadTypeId,
                agent: agent
            })

            // TODO: Implement actual assignment logic
            // For now, just cancel the form
            $scope.cancelAddingAgentToLeadType(stageId, leadTypeId)

            // Refresh data
            $scope.invalidateAssignedAgentsCache()
        }

        // Close dropdowns when clicking outside
        $scope.closeAllDropdowns = function () {
            $scope.showAgentDropdown = {}
            $scope.showAgentDropdownLeadType = {}
            $scope.showLeadTypeDropdown = {}
            $scope.showEditLeadTypeDropdown = {}
            console.log('Closed all dropdowns')
        }

        // Close specific lead type dropdown
        $scope.closeLeadTypeDropdown = function (stageId) {
            $scope.showLeadTypeDropdown[stageId] = false
            console.log('Closed lead type dropdown for stage', stageId)
        }

        // Close specific edit lead type dropdown
        $scope.closeEditLeadTypeDropdown = function (stageId, agentId) {
            var key = stageId + '_' + agentId
            $scope.showEditLeadTypeDropdown[key] = false
            console.log('Closed edit lead type dropdown for', key)
        }



        // Inline lead type editing functions
        $scope.startEditingAgentLeadTypes = function (stageId, agent) {
            var key = stageId + '_' + agent.id
            $scope.editingAgentLeadTypes[key] = true
            $scope.editingLeadTypes[key] = {}
            $scope.editLeadTypeSearch[key] = ''
            $scope.showEditLeadTypeDropdown[key] = false

            // Pre-populate with current lead type assignments
            if (agent.assignedLeadTypes) {
                agent.assignedLeadTypes.forEach(function(leadType) {
                    $scope.editingLeadTypes[key][leadType.id] = true
                })
            }
        }

        $scope.cancelEditingAgentLeadTypes = function (stageId, agentId) {
            var key = stageId + '_' + agentId
            $scope.editingAgentLeadTypes[key] = false
            $scope.editingLeadTypes[key] = {}
            $scope.editLeadTypeSearch[key] = ''
            $scope.showEditLeadTypeDropdown[key] = false
        }

        $scope.toggleEditLeadTypeSelection = function (stageId, agentId, leadType) {
            var key = stageId + '_' + agentId
            if (!$scope.editingLeadTypes[key]) {
                $scope.editingLeadTypes[key] = {}
            }

            // Reliable toggle - convert to boolean first
            var currentValue = !!$scope.editingLeadTypes[key][leadType.id]
            $scope.editingLeadTypes[key][leadType.id] = !currentValue

            console.log('Toggled edit lead type', leadType.name, 'for agent', agentId, 'from', currentValue, 'to', !currentValue)
        }

        $scope.removeEditLeadTypeSelection = function (stageId, agentId, leadTypeId) {
            var key = stageId + '_' + agentId
            if ($scope.editingLeadTypes[key]) {
                $scope.editingLeadTypes[key][leadTypeId] = false
            }
        }

        $scope.getEditingLeadTypesDisplay = function (stageId, agentId) {
            var key = stageId + '_' + agentId
            if (!$scope.editingLeadTypes[key] || !$scope.leadTypes) return []

            return $scope.leadTypes.filter(function(leadType) {
                return $scope.editingLeadTypes[key][leadType.id]
            })
        }

        $scope.saveAgentLeadTypes = function (stageId, agent) {
            var key = stageId + '_' + agent.id
            var selectedLeadTypes = $scope.editingLeadTypes[key]

            console.log('Saving agent lead types:', {
                stageId: stageId,
                agent: agent,
                leadTypes: selectedLeadTypes
            })

            // TODO: Implement actual save logic
            // For now, just cancel the editing
            $scope.cancelEditingAgentLeadTypes(stageId, agent.id)

            // Refresh data
            $scope.invalidateAssignedAgentsCache()
        }

        // Add click handler to document to close dropdowns
        angular.element(document).on('click', function(event) {
            var target = angular.element(event.target)

            // Don't close if clicking on dropdown items, checkboxes, or input fields
            if (target.closest('.leadtype-dropdown').length > 0 ||
                target.closest('.agent-dropdown').length > 0 ||
                target.hasClass('form-control') ||
                target.hasClass('leadtype-dropdown-item') ||
                target.closest('.leadtype-dropdown-item').length > 0 ||
                target.is('input[type="checkbox"]') ||
                target.closest('input[type="checkbox"]').length > 0) {
                return // Don't close dropdowns
            }

            // Close dropdowns if clicking outside
            $scope.$apply(function() {
                $scope.closeAllDropdowns()
            })
        })

        // Also close dropdowns when pressing Escape key
        angular.element(document).on('keydown', function(event) {
            if (event.keyCode === 27) { // Escape key
                $scope.$apply(function() {
                    $scope.closeAllDropdowns()
                })
            }
        })

        $scope.editAgentLeadTypes = function (stageId, agent) {
            var stage = $scope.stages.find(function(s) { return s.id === stageId })
            $scope.modalData = {
                stageId: stageId,
                agentId: agent.id,
                stageName: stage ? stage.name : 'Unknown Stage',
                agentName: agent.name,
                selectedLeadTypes: {}
            }

            // Pre-populate with current lead type assignments
            if (agent.assignedLeadTypes) {
                agent.assignedLeadTypes.forEach(function(leadType) {
                    $scope.modalData.selectedLeadTypes[leadType.id] = true
                })
            }

            $('#editAgentLeadTypesModal').modal('show')
        }

        $scope.updateAgentLeadTypes = function () {
            // Implementation would update lead type assignments
            console.log('Updating agent lead types:', $scope.modalData)
            $('#editAgentLeadTypesModal').modal('hide')
            // TODO: Implement actual update logic
        }

        $scope.removeAgentFromStage = function (stageId, agentId) {
            if (confirm('Are you sure you want to remove this agent from the stage?')) {
                // Implementation would remove all assignments for this agent in this stage
                console.log('Removing agent from stage:', stageId, agentId)
                // TODO: Implement actual removal logic
            }
        }

        $scope.removeAgentFromStageLeadType = function (stageId, leadTypeId, agentId) {
            if (confirm('Are you sure you want to remove this agent from this lead type?')) {
                // Implementation would remove specific assignment
                console.log('Removing agent from stage/lead type:', stageId, leadTypeId, agentId)
                // TODO: Implement actual removal logic
            }
        }

        // Stage assignment functions
        $scope.markStageAssignmentChanged = function (stageId, agentId) {
            var originalState = $scope.originalStageAssignments[stageId][agentId]

            $scope.stageAssignments[stageId].agents[agentId].changed =
                $scope.stageAssignments[stageId].agents[agentId].assigned !== originalState.originallyAssigned

            // Update stage flags
            $scope.stageAssignments[stageId].hasChanges = Object.keys($scope.stageAssignments[stageId].agents).some(function(id) {
                return $scope.stageAssignments[stageId].agents[id].changed
            })

            $scope.stageAssignments[stageId].hasAssignments = Object.keys($scope.stageAssignments[stageId].agents).some(function(id) {
                return $scope.stageAssignments[stageId].agents[id].assigned
            })

            $scope.stageAssignments[stageId].allAssigned = $scope.agents.every(function(agent) {
                return $scope.stageAssignments[stageId].agents[agent.id].assigned
            })

            console.log('🔍 markStageAssignmentChanged: Stage assignment changed for stage', stageId, 'agent', agentId)
        }

        $scope.assignAllAgentsToStage = function (stageId) {
            $scope.agents.forEach(function(agent) {
                $scope.stageAssignments[stageId].agents[agent.id].assigned = true
                $scope.markStageAssignmentChanged(stageId, agent.id)
            })
        }

        $scope.unassignAllAgentsFromStage = function (stageId) {
            $scope.agents.forEach(function(agent) {
                $scope.stageAssignments[stageId].agents[agent.id].assigned = false
                $scope.markStageAssignmentChanged(stageId, agent.id)
            })
        }

        // Updated hasChanges function to include stage assignments
        $scope.hasChanges = function () {
            var matrixChanges = $scope.assignmentMatrix.some(function(assignment) {
                return assignment.hasChanges
            })

            var stageChanges = Object.keys($scope.stageAssignments).some(function(stageId) {
                return $scope.stageAssignments[stageId].hasChanges
            })

            return matrixChanges || stageChanges
        }
        
        // Initialize when campaign is available
        var hasLoaded = false
        $scope.$watch('campaign.id', function (newVal, oldVal) {
            console.log('🔍 Campaign ID watcher triggered:', newVal, 'oldVal:', oldVal, 'hasLoaded:', hasLoaded)
            if (newVal && !hasLoaded) {
                hasLoaded = true
                $scope.loadAgentAssignments()
            }
        })

        // Also try to load immediately if campaign is already available
        if ($scope.campaign && $scope.campaign.id && !hasLoaded) {
            console.log('🔍 Campaign already available, loading immediately')
            hasLoaded = true
            $scope.loadAgentAssignments()
        }

        // Third view: Agent → Stage → Lead Types functions
        $scope.toggleAgentExpansion = function (agentId) {
            $scope.expandedAgents[agentId] = !$scope.expandedAgents[agentId]
        }

        $scope.isAgentExpanded = function (agentId) {
            return !!$scope.expandedAgents[agentId]
        }

        // Bulk assignment functions for third view
        $scope.toggleBulkAgentSelection = function (agent) {
            if (!$scope.selectedBulkAgents[agent.id]) {
                $scope.selectedBulkAgents[agent.id] = true
            } else {
                $scope.selectedBulkAgents[agent.id] = false
            }
            console.log('Toggled bulk agent', agent.name, 'to', $scope.selectedBulkAgents[agent.id])
        }

        $scope.removeBulkAgentSelection = function (agentId) {
            $scope.selectedBulkAgents[agentId] = false
        }

        $scope.getBulkSelectedAgentsDisplay = function () {
            if (!$scope.agents) return []
            return $scope.agents.filter(function(agent) {
                return $scope.selectedBulkAgents[agent.id]
            })
        }

        $scope.toggleBulkStageSelection = function (stage) {
            if (!$scope.selectedBulkStages[stage.id]) {
                $scope.selectedBulkStages[stage.id] = true
            } else {
                $scope.selectedBulkStages[stage.id] = false
            }
            console.log('Toggled bulk stage', stage.name, 'to', $scope.selectedBulkStages[stage.id])
        }

        $scope.removeBulkStageSelection = function (stageId) {
            $scope.selectedBulkStages[stageId] = false
        }

        $scope.getBulkSelectedStagesDisplay = function () {
            if (!$scope.stages) return []
            return $scope.stages.filter(function(stage) {
                return $scope.selectedBulkStages[stage.id]
            })
        }

        $scope.toggleBulkLeadTypeSelection = function (leadType) {
            if (!$scope.selectedBulkLeadTypes[leadType.id]) {
                $scope.selectedBulkLeadTypes[leadType.id] = true
            } else {
                $scope.selectedBulkLeadTypes[leadType.id] = false
            }
            console.log('Toggled bulk lead type', leadType.name, 'to', $scope.selectedBulkLeadTypes[leadType.id])
        }

        $scope.removeBulkLeadTypeSelection = function (leadTypeId) {
            $scope.selectedBulkLeadTypes[leadTypeId] = false
        }

        $scope.getBulkSelectedLeadTypesDisplay = function () {
            if (!$scope.leadTypes) return []
            return $scope.leadTypes.filter(function(leadType) {
                return $scope.selectedBulkLeadTypes[leadType.id]
            })
        }

        $scope.performBulkAssignment = function () {
            var selectedAgents = $scope.getBulkSelectedAgentsDisplay()
            var selectedStages = $scope.getBulkSelectedStagesDisplay()
            var selectedLeadTypes = $scope.getBulkSelectedLeadTypesDisplay()

            console.log('Performing bulk assignment:', {
                agents: selectedAgents,
                stages: selectedStages,
                leadTypes: selectedLeadTypes
            })

            // TODO: Implement actual bulk assignment logic
            // For now, just clear selections
            $scope.clearBulkSelections()

            // Refresh data
            $scope.loadAgentAssignments()
        }

        $scope.clearBulkSelections = function () {
            $scope.selectedBulkAgents = {}
            $scope.selectedBulkStages = {}
            $scope.selectedBulkLeadTypes = {}
            $scope.bulkAgentSearch = ''
            $scope.bulkStageSearch = ''
            $scope.bulkLeadTypeSearch = ''
            $scope.showBulkAgentDropdown = false
            $scope.showBulkStageDropdown = false
            $scope.showBulkLeadTypeDropdown = false
        }

        $scope.loadAgentAssignments = function () {
            console.log('Loading agent assignments...')
            console.log('Available data:', {
                agents: $scope.agents ? $scope.agents.length : 0,
                stages: $scope.stages ? $scope.stages.length : 0,
                leadTypes: $scope.leadTypes ? $scope.leadTypes.length : 0
            })

            if (!$scope.agents || !$scope.stages || !$scope.leadTypes) {
                console.log('Missing data for agent assignments, will retry when data is available')
                // Initialize empty array so the template doesn't break
                $scope.agentAssignments = []
                return
            }

            // Create agent-centric view with mock data for now
            $scope.agentAssignments = $scope.agents.map(function(agent, index) {
                // Create some mock assignments for demonstration
                var mockStageAssignments = []

                // Give each agent 1-3 random stage assignments for demo
                var numAssignments = Math.min(1 + (index % 3), $scope.stages.length)
                for (var i = 0; i < numAssignments; i++) {
                    var stage = $scope.stages[i % $scope.stages.length]
                    var numLeadTypes = Math.min(2 + (index % 3), $scope.leadTypes.length)
                    var assignedLeadTypes = []

                    // Assign some lead types
                    for (var j = 0; j < numLeadTypes; j++) {
                        assignedLeadTypes.push($scope.leadTypes[j % $scope.leadTypes.length])
                    }

                    mockStageAssignments.push({
                        stage: stage,
                        assignedLeadTypes: assignedLeadTypes
                    })
                }

                return {
                    id: agent.id,
                    name: agent.name,
                    assignedStages: mockStageAssignments
                }
            })

            console.log('Loaded agent assignments:', $scope.agentAssignments)
        }

        $scope.removeAllAgentAssignments = function (agentId) {
            console.log('Removing all assignments for agent', agentId)
            // TODO: Implement remove all assignments logic
            $scope.loadAgentAssignments()
        }

        $scope.startEditingAgentStageLeadTypes = function (agentId, stageId) {
            var key = agentId + '_' + stageId
            console.log('Starting to edit lead types for agent', agentId, 'in stage', stageId)

            // Initialize editing state
            $scope.editingAgentStageLeadTypes[key] = {}
            $scope.editAgentStageLeadTypeSearch[key] = ''
            $scope.showEditAgentStageLeadTypeDropdown[key] = false

            // Find current lead type assignments for this agent-stage combination
            var agent = $scope.agentAssignments.find(function(a) { return a.id === agentId })
            if (agent) {
                var stageAssignment = agent.assignedStages.find(function(s) { return s.stage.id === stageId })
                if (stageAssignment && stageAssignment.assignedLeadTypes) {
                    // Pre-populate with current assignments
                    stageAssignment.assignedLeadTypes.forEach(function(leadType) {
                        $scope.editingAgentStageLeadTypes[key][leadType.id] = true
                    })
                }
            }

            console.log('Initialized editing state for', key, 'with lead types:', $scope.editingAgentStageLeadTypes[key])
        }

        $scope.removeAgentFromStageAssignment = function (agentId, stageId) {
            console.log('Removing agent', agentId, 'from stage', stageId)
            // TODO: Implement remove agent from stage logic
            $scope.loadAgentAssignments()
        }

        // Agent-stage lead type editing functions
        $scope.toggleEditAgentStageLeadTypeSelection = function (agentId, stageId, leadType) {
            var key = agentId + '_' + stageId
            if (!$scope.editingAgentStageLeadTypes[key]) {
                $scope.editingAgentStageLeadTypes[key] = {}
            }

            var currentValue = !!$scope.editingAgentStageLeadTypes[key][leadType.id]
            $scope.editingAgentStageLeadTypes[key][leadType.id] = !currentValue

            console.log('Toggled lead type', leadType.name, 'for agent-stage', key, 'from', currentValue, 'to', !currentValue)
        }

        $scope.removeEditAgentStageLeadTypeSelection = function (agentId, stageId, leadTypeId) {
            var key = agentId + '_' + stageId
            if ($scope.editingAgentStageLeadTypes[key]) {
                $scope.editingAgentStageLeadTypes[key][leadTypeId] = false
            }
        }

        $scope.getEditingAgentStageLeadTypesDisplay = function (agentId, stageId) {
            var key = agentId + '_' + stageId
            if (!$scope.editingAgentStageLeadTypes[key] || !$scope.leadTypes) return []

            return $scope.leadTypes.filter(function(leadType) {
                return $scope.editingAgentStageLeadTypes[key][leadType.id]
            })
        }

        $scope.saveAgentStageLeadTypes = function (agentId, stageId) {
            var key = agentId + '_' + stageId
            var selectedLeadTypes = $scope.getEditingAgentStageLeadTypesDisplay(agentId, stageId)

            console.log('Saving lead types for agent-stage', key, ':', selectedLeadTypes)

            // TODO: Implement actual save logic
            // For now, update the local data structure
            var agent = $scope.agentAssignments.find(function(a) { return a.id === agentId })
            if (agent) {
                var stageAssignment = agent.assignedStages.find(function(s) { return s.stage.id === stageId })
                if (stageAssignment) {
                    stageAssignment.assignedLeadTypes = selectedLeadTypes
                }
            }

            // Clear editing state
            $scope.cancelEditingAgentStageLeadTypes(agentId, stageId)
        }

        $scope.cancelEditingAgentStageLeadTypes = function (agentId, stageId) {
            var key = agentId + '_' + stageId

            // Clear editing state
            delete $scope.editingAgentStageLeadTypes[key]
            delete $scope.editAgentStageLeadTypeSearch[key]
            delete $scope.showEditAgentStageLeadTypeDropdown[key]

            console.log('Cancelled editing for agent-stage', key)
        }

        // Add agent to stages functions
        $scope.startAddingAgentToStages = function (agentId) {
            $scope.addingAgentToStages[agentId] = true
            $scope.selectedAgentStages[agentId] = {}
            $scope.selectedAgentLeadTypes[agentId] = {}
            $scope.newAgentStageSearch[agentId] = ''
            $scope.newAgentLeadTypeSearch[agentId] = ''
            $scope.showAgentStageDropdown[agentId] = false
            $scope.showAgentLeadTypeDropdown[agentId] = false

            console.log('Started adding agent', agentId, 'to stages')
        }

        $scope.cancelAddingAgentToStages = function (agentId) {
            $scope.addingAgentToStages[agentId] = false
            delete $scope.selectedAgentStages[agentId]
            delete $scope.selectedAgentLeadTypes[agentId]
            delete $scope.newAgentStageSearch[agentId]
            delete $scope.newAgentLeadTypeSearch[agentId]
            delete $scope.showAgentStageDropdown[agentId]
            delete $scope.showAgentLeadTypeDropdown[agentId]

            console.log('Cancelled adding agent', agentId, 'to stages')
        }

        $scope.toggleAgentStageSelection = function (agentId, stage) {
            if (!$scope.selectedAgentStages[agentId]) {
                $scope.selectedAgentStages[agentId] = {}
            }

            var currentValue = !!$scope.selectedAgentStages[agentId][stage.id]
            $scope.selectedAgentStages[agentId][stage.id] = !currentValue

            console.log('Toggled stage', stage.name, 'for agent', agentId, 'from', currentValue, 'to', !currentValue)
        }

        $scope.removeAgentStageSelection = function (agentId, stageId) {
            if ($scope.selectedAgentStages[agentId]) {
                $scope.selectedAgentStages[agentId][stageId] = false
            }
        }

        $scope.getSelectedAgentStagesDisplay = function (agentId) {
            if (!$scope.selectedAgentStages[agentId] || !$scope.stages) return []

            return $scope.stages.filter(function(stage) {
                return $scope.selectedAgentStages[agentId][stage.id]
            })
        }

        $scope.toggleAgentLeadTypeSelection = function (agentId, leadType) {
            if (!$scope.selectedAgentLeadTypes[agentId]) {
                $scope.selectedAgentLeadTypes[agentId] = {}
            }

            var currentValue = !!$scope.selectedAgentLeadTypes[agentId][leadType.id]
            $scope.selectedAgentLeadTypes[agentId][leadType.id] = !currentValue

            console.log('Toggled lead type', leadType.name, 'for agent', agentId, 'from', currentValue, 'to', !currentValue)
        }

        $scope.removeAgentLeadTypeSelection = function (agentId, leadTypeId) {
            if ($scope.selectedAgentLeadTypes[agentId]) {
                $scope.selectedAgentLeadTypes[agentId][leadTypeId] = false
            }
        }

        $scope.getSelectedAgentLeadTypesDisplay = function (agentId) {
            if (!$scope.selectedAgentLeadTypes[agentId] || !$scope.leadTypes) return []

            return $scope.leadTypes.filter(function(leadType) {
                return $scope.selectedAgentLeadTypes[agentId][leadType.id]
            })
        }

        $scope.getAvailableStagesForAgent = function (agentId) {
            if (!$scope.stages) return []

            // Filter out stages already assigned to this agent
            var assignedStageIds = {}
            var agent = $scope.agentAssignments.find(function(a) { return a.id === agentId })
            if (agent && agent.assignedStages) {
                agent.assignedStages.forEach(function(stageAssignment) {
                    assignedStageIds[stageAssignment.stage.id] = true
                })
            }

            return $scope.stages.filter(function(stage) {
                return !assignedStageIds[stage.id]
            })
        }

        $scope.addAgentToStagesInline = function (agentId) {
            var selectedStages = $scope.getSelectedAgentStagesDisplay(agentId)
            var selectedLeadTypes = $scope.getSelectedAgentLeadTypesDisplay(agentId)

            console.log('Adding agent to stages:', {
                agentId: agentId,
                stages: selectedStages,
                leadTypes: selectedLeadTypes
            })

            // TODO: Implement actual save logic
            // For now, just clear the selections
            $scope.cancelAddingAgentToStages(agentId)

            // Refresh data
            $scope.loadAgentAssignments()
        }

        // Watch for assignment mode changes to load appropriate data
        $scope.$watch('assignmentMode', function(newMode, oldMode) {
            if (newMode === 'agentStage' && newMode !== oldMode) {
                console.log('Switched to agent-stage view, loading agent assignments')
                $scope.loadAgentAssignments()
            }
        })

        // Watch for data availability to load agent assignments
        $scope.$watch('[agents, stages, leadTypes]', function(newValues) {
            var agents = newValues[0]
            var stages = newValues[1]
            var leadTypes = newValues[2]

            if (agents && stages && leadTypes && agents.length > 0 && stages.length > 0 && leadTypes.length > 0) {
                console.log('All data available, loading agent assignments')
                $scope.loadAgentAssignments()
            }
        }, true)
    })
