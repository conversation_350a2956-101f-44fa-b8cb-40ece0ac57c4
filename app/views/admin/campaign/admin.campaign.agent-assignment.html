<div class="agent-assignment-container">
    <!-- Loading indicator -->
    <div ng-if="loading" class="text-center" style="padding: 40px;">
        <i class="fa fa-spinner fa-spin fa-2x"></i>
        <p>Loading agent assignments...</p>
        <small class="text-muted">If this takes too long, please refresh the page</small>
        <br><br>
        <button class="btn btn-warning btn-sm" ng-click="loading = false; loadAgentAssignments()">
            Force Retry
        </button>
        <button class="btn btn-info btn-sm" onclick="window.forceLoadAgentAssignments()">
            Manual Load (JS)
        </button>
        <button class="btn btn-success btn-sm" onclick="window.testCampaignAPI()">
            Test API
        </button>
    </div>
    
    <!-- Debug info (remove in production) -->
    <div ng-if="!loading && currentViewData.length === 0" class="alert alert-info">
        <strong>Debug Info:</strong>
        Campaign ID: {{ campaign.id || 'Not available' }} |
        Current View: {{ currentView }} |
        Data Available: {{ !!hierarchicalData }} |
        View Data Length: {{ currentViewData.length }}
    </div>

    <!-- Main content -->
    <div ng-if="!loading">
        <!-- Assignment Mode Selector -->
        <div class="assignment-mode-selector">
            <h4>Agent Assignment Management</h4>
            <div class="btn-group view-mode-selector" role="group">
                <button type="button"
                        class="btn"
                        ng-class="assignmentMode === 'stageAgent' ? 'btn-primary' : 'btn-outline-primary'"
                        ng-click="assignmentMode = 'stageAgent'">
                    <i class="fa fa-sitemap"></i>
                    Stage → Agent → Lead Types
                </button>
                <button type="button"
                        class="btn"
                        ng-class="assignmentMode === 'stageLeadType' ? 'btn-warning' : 'btn-outline-warning'"
                        ng-click="assignmentMode = 'stageLeadType'">
                    <i class="fa fa-tags"></i>
                    Stage → Lead Type → Agents
                </button>
                <button type="button"
                        class="btn"
                        ng-class="assignmentMode === 'agentStage' ? 'btn-success' : 'btn-outline-success'"
                        ng-click="assignmentMode = 'agentStage'">
                    <i class="fa fa-users"></i>
                    Agent → Stage → Lead Types
                </button>
            </div>


        </div>
        

        
        <!-- Assignment Editor -->
        <div class="assignment-editor">
            <!-- Interactive Assignment Tree -->
            <div class="assignment-tree-container">
                <div class="assignment-tree">

                    <!-- Stage → Agent → Lead Types Mode -->
                    <div ng-if="assignmentMode === 'stageAgent'">
                        <div ng-repeat="stage in stages" class="tree-stage-node">
                            <div class="stage-header-row">
                                <button class="btn btn-xs btn-link tree-toggle"
                                        ng-click="toggleStageExpanded(stage.id)">
                                    <i class="fa" ng-class="isStageExpanded(stage.id) ? 'fa-minus-square-o' : 'fa-plus-square-o'"></i>
                                </button>

                                <div class="stage-info">
                                    <h5 class="stage-name">
                                        <i class="fa fa-folder text-primary"></i>
                                        {{ stage.name }}
                                    </h5>
                                </div>

                                <div class="stage-summary">
                                    <span class="assigned-count">
                                        {{ stageAssignedAgents[stage.id].length }} agents assigned
                                    </span>
                                </div>
                            </div>

                            <!-- Assigned Agents List (Expandable) -->
                            <div ng-if="isStageExpanded(stage.id)" class="agents-list">
                                <!-- Default text when no agents assigned -->
                                <div ng-if="stageAssignedAgents[stage.id].length === 0" class="no-agents-message">
                                    <i class="fa fa-info-circle text-muted"></i>
                                    <span class="text-muted">No agents assigned to this stage yet. Click "Add Agent to {{ stage.name }}" to get started.</span>
                                </div>

                                <!-- Show only assigned agents -->
                                <div ng-repeat="agent in stageAssignedAgents[stage.id]">
                                    <!-- Normal Agent Display -->
                                    <div ng-if="!editingAgentLeadTypes[stage.id + '_' + agent.id]" class="agent-assignment-row">
                                        <div class="agent-info">
                                            <span class="agent-name">
                                                <i class="fa fa-user text-info"></i>
                                                {{ agent.name }}
                                            </span>
                                        </div>

                                        <div class="agent-leadtypes">
                                            <strong>Lead Types:</strong>
                                            <span ng-repeat="leadType in agent.assignedLeadTypes" class="label label-success" style="margin: 2px;">
                                                {{ leadType.name }}
                                            </span>
                                            <span ng-if="agent.assignedLeadTypes.length === 0" class="text-muted">
                                                No specific lead types (gets all)
                                            </span>
                                        </div>

                                        <div class="agent-actions">
                                            <button class="btn btn-xs btn-primary"
                                                    ng-click="startEditingAgentLeadTypes(stage.id, agent)"
                                                    title="Edit lead type assignments for this agent">
                                                <i class="fa fa-edit"></i>
                                                Edit Lead Types
                                            </button>
                                            <button class="btn btn-xs btn-danger"
                                                    ng-click="removeAgentFromStage(stage.id, agent.id)"
                                                    title="Remove agent from this stage">
                                                <i class="fa fa-times"></i>
                                                Remove
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Inline Lead Type Editor -->
                                    <div ng-if="editingAgentLeadTypes[stage.id + '_' + agent.id]" class="inline-leadtype-editor">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="agent-info-edit">
                                                    <span class="agent-name">
                                                        <i class="fa fa-user text-info"></i>
                                                        {{ agent.name }}
                                                    </span>
                                                    <small class="text-muted">Editing lead types</small>
                                                </div>
                                            </div>

                                            <div class="col-md-7">
                                                <div class="leadtype-editor">
                                                    <label class="control-label">Select Lead Types:</label>
                                                    <input type="text"
                                                           class="form-control form-control-sm"
                                                           ng-model="editLeadTypeSearch[stage.id + '_' + agent.id]"
                                                           placeholder="Search lead types..."
                                                           ng-focus="showEditLeadTypeDropdown[stage.id + '_' + agent.id] = true">

                                                    <!-- Edit Lead Type Dropdown -->
                                                    <div ng-if="showEditLeadTypeDropdown[stage.id + '_' + agent.id]"
                                                         class="leadtype-dropdown"
                                                         ng-click="$event.stopPropagation()">
                                                        <div ng-repeat="leadType in leadTypes | filter:editLeadTypeSearch[stage.id + '_' + agent.id]"
                                                             class="leadtype-dropdown-item"
                                                             ng-click="toggleEditLeadTypeSelection(stage.id, agent.id, leadType)">
                                                            <input type="checkbox"
                                                                   ng-checked="editingLeadTypes[stage.id + '_' + agent.id][leadType.id]"
                                                                   ng-click="toggleEditLeadTypeSelection(stage.id, agent.id, leadType); $event.stopPropagation()">
                                                            <span ng-class="{'selected': editingLeadTypes[stage.id + '_' + agent.id][leadType.id]}">
                                                                {{ leadType.name }}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <!-- Selected Lead Types Display with Scrolling -->
                                                    <div ng-if="getEditingLeadTypesDisplay(stage.id, agent.id).length > 0" class="selected-leadtypes-scrollable">
                                                        <span ng-repeat="leadType in getEditingLeadTypesDisplay(stage.id, agent.id)"
                                                              class="leadtype-tag">
                                                            {{ leadType.name }}
                                                            <i class="fa fa-times" ng-click="removeEditLeadTypeSelection(stage.id, agent.id, leadType.id)"></i>
                                                        </span>
                                                    </div>

                                                    <small class="text-muted">Leave empty to assign ALL lead types</small>
                                                </div>
                                            </div>

                                            <div class="col-md-2">
                                                <div class="edit-actions">
                                                    <button class="btn btn-primary btn-xs btn-block"
                                                            ng-click="saveAgentLeadTypes(stage.id, agent)"
                                                            title="Save changes"
                                                            style="margin-bottom: 5px;">
                                                        <i class="fa fa-check"></i>
                                                        Save
                                                    </button>
                                                    <button class="btn btn-white btn-xs btn-block"
                                                            ng-click="cancelEditingAgentLeadTypes(stage.id, agent.id)"
                                                            title="Cancel editing">
                                                        <i class="fa fa-times"></i>
                                                        Cancel
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Add New Agent Inline Form -->
                                <div class="add-agent-row">
                                    <div ng-if="!addingAgentToStage[stage.id]">
                                        <button class="btn btn-success btn-sm"
                                                ng-click="startAddingAgentToStage(stage.id)"
                                                title="Add new agent to this stage">
                                            <i class="fa fa-plus"></i>
                                            Add Agent to {{ stage.name }}
                                        </button>
                                    </div>

                                    <!-- Inline Agent Addition Form -->
                                    <div ng-if="addingAgentToStage[stage.id]" class="inline-agent-form">
                                        <div class="row">
                                            <div class="col-md-4 col-sm-12">
                                                <div class="form-group">
                                                    <label class="control-label">Select Agents:</label>
                                                    <input type="text"
                                                           class="form-control form-control-sm"
                                                           ng-model="newAgentSearch[stage.id]"
                                                           placeholder="Search and select agents..."
                                                           ng-focus="showAgentDropdown[stage.id] = true">

                                                    <!-- Agent Multi-Select Dropdown -->
                                                    <div ng-if="showAgentDropdown[stage.id]"
                                                         class="agent-dropdown"
                                                         ng-click="$event.stopPropagation()">
                                                        <div ng-repeat="agent in getAvailableAgents(stage.id) | filter:newAgentSearch[stage.id]"
                                                             class="agent-dropdown-item"
                                                             ng-click="toggleAgentSelection(stage.id, agent)">
                                                            <input type="checkbox"
                                                                   ng-checked="selectedNewAgents[stage.id][agent.id]"
                                                                   ng-click="toggleAgentSelection(stage.id, agent); $event.stopPropagation()">
                                                            <span ng-class="{'selected': selectedNewAgents[stage.id][agent.id]}">
                                                                {{ agent.name }}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <!-- Selected Agents Display -->
                                                    <div ng-if="getSelectedAgentsDisplay(stage.id).length > 0" class="selected-agents">
                                                        <span ng-repeat="agent in getSelectedAgentsDisplay(stage.id)"
                                                              class="agent-tag">
                                                            {{ agent.name }}
                                                            <i class="fa fa-times" ng-click="removeAgentSelection(stage.id, agent.id)"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-5 col-sm-12">
                                                <div class="form-group">
                                                    <label class="control-label">Lead Types:</label>
                                                    <div class="leadtype-multiselect">
                                                        <input type="text"
                                                               class="form-control form-control-sm"
                                                               ng-model="leadTypeSearch[stage.id]"
                                                               placeholder="Search and select lead types..."
                                                               ng-focus="showLeadTypeDropdown[stage.id] = true">

                                                        <!-- Lead Type Dropdown -->
                                                        <div ng-if="showLeadTypeDropdown[stage.id]"
                                                             class="leadtype-dropdown"
                                                             ng-click="$event.stopPropagation()">
                                                            <div ng-repeat="leadType in leadTypes | filter:leadTypeSearch[stage.id]"
                                                                 class="leadtype-dropdown-item"
                                                                 ng-click="toggleLeadTypeSelection(stage.id, leadType)">
                                                                <input type="checkbox"
                                                                       ng-checked="newAgentLeadTypes[stage.id][leadType.id]"
                                                                       ng-click="toggleLeadTypeSelection(stage.id, leadType); $event.stopPropagation()">
                                                                <span ng-class="{'selected': newAgentLeadTypes[stage.id][leadType.id]}">
                                                                    {{ leadType.name }}
                                                                </span>
                                                            </div>
                                                        </div>

                                                        <!-- Selected Lead Types Display -->
                                                        <div ng-if="getSelectedLeadTypesForStageDisplay(stage.id).length > 0" class="selected-leadtypes">
                                                            <span ng-repeat="leadType in getSelectedLeadTypesForStageDisplay(stage.id)"
                                                                  class="leadtype-tag">
                                                                {{ leadType.name }}
                                                                <i class="fa fa-times" ng-click="removeLeadTypeSelection(stage.id, leadType.id)"></i>
                                                            </span>
                                                        </div>

                                                        <small class="text-muted">Leave empty to assign ALL lead types</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-3 col-sm-12">
                                                <div class="form-group">
                                                    <label class="control-label">&nbsp;</label>
                                                    <div class="button-group">
                                                        <button class="btn btn-primary btn-xs btn-block"
                                                                ng-click="addAgentsToStageInline(stage.id)"
                                                                ng-disabled="getSelectedAgentsDisplay(stage.id).length === 0"
                                                                title="Add selected agents"
                                                                style="margin-bottom: 5px;">
                                                            <i class="fa fa-check"></i>
                                                            Add ({{ getSelectedAgentsDisplay(stage.id).length }})
                                                        </button>
                                                        <button class="btn btn-white btn-xs btn-block"
                                                                ng-click="cancelAddingAgentToStage(stage.id)"
                                                                title="Cancel">
                                                            <i class="fa fa-times"></i>
                                                            Cancel
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Selected Agent Preview -->
                                        <div ng-if="selectedNewAgent[stage.id]" class="selected-agent-preview">
                                            <strong>Selected:</strong> {{ selectedNewAgent[stage.id].name }}
                                            <span ng-if="getSelectedLeadTypesCountForStage(stage.id) === 0" class="text-info">
                                                (will get all lead types)
                                            </span>
                                            <span ng-if="getSelectedLeadTypesCountForStage(stage.id) > 0" class="text-warning">
                                                ({{ getSelectedLeadTypesCountForStage(stage.id) }} specific lead types)
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stage → Lead Type → Agents Mode -->
                    <div ng-if="assignmentMode === 'stageLeadType'">
                        <div ng-repeat="stage in stages" class="tree-stage-node">
                            <div class="stage-header-row">
                                <button class="btn btn-xs btn-link tree-toggle"
                                        ng-click="toggleStageExpanded(stage.id)">
                                    <i class="fa" ng-class="isStageExpanded(stage.id) ? 'fa-minus-square-o' : 'fa-plus-square-o'"></i>
                                </button>

                                <div class="stage-info">
                                    <h5 class="stage-name">
                                        <i class="fa fa-folder text-primary"></i>
                                        {{ stage.name }}
                                    </h5>
                                </div>

                                <div class="stage-summary">
                                    <span class="assigned-count">
                                        {{ getStageLeadTypeAssignmentsCount(stage.id) }} lead type assignments
                                    </span>
                                </div>
                            </div>

                            <!-- Lead Types List (Expandable) -->
                            <div ng-if="isStageExpanded(stage.id)" class="leadtypes-list">
                                <div ng-repeat="leadType in leadTypes" class="tree-leadtype-node">
                                    <div class="leadtype-header-row">
                                        <button class="btn btn-xs btn-link tree-toggle"
                                                ng-click="toggleLeadTypeExpanded(stage.id, leadType.id)">
                                            <i class="fa" ng-class="isLeadTypeExpanded(stage.id, leadType.id) ? 'fa-minus-square-o' : 'fa-plus-square-o'"></i>
                                        </button>

                                        <div class="leadtype-info">
                                            <h6 class="leadtype-name">
                                                <i class="fa fa-tag text-warning"></i>
                                                {{ leadType.name }}
                                            </h6>
                                        </div>

                                        <div class="leadtype-summary">
                                            <span class="assigned-count">
                                                {{ stageLeadTypeAssignedAgents[stage.id + '_' + leadType.id].length }} agents assigned
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Assigned Agents List for this Lead Type (Expandable) -->
                                    <div ng-if="isLeadTypeExpanded(stage.id, leadType.id)" class="agents-list">
                                        <!-- Default text when no agents assigned -->
                                        <div ng-if="stageLeadTypeAssignedAgents[stage.id + '_' + leadType.id].length === 0" class="no-agents-message">
                                            <i class="fa fa-info-circle text-muted"></i>
                                            <span class="text-muted">No agents assigned to {{ leadType.name }} in this stage yet. Click "Add Agent to {{ leadType.name }}" to get started.</span>
                                        </div>

                                        <!-- Show only assigned agents -->
                                        <div ng-repeat="agent in stageLeadTypeAssignedAgents[stage.id + '_' + leadType.id]" class="agent-assignment-row">
                                            <div class="agent-info">
                                                <span class="agent-name">
                                                    <i class="fa fa-user text-info"></i>
                                                    {{ agent.name }}
                                                </span>
                                            </div>

                                            <div class="agent-actions">
                                                <button class="btn btn-xs btn-danger"
                                                        ng-click="removeAgentFromStageLeadType(stage.id, leadType.id, agent.id)"
                                                        title="Remove agent from this stage/lead type">
                                                    <i class="fa fa-times"></i>
                                                    Remove
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Add New Agent Inline Form -->
                                        <div class="add-agent-row">
                                            <div ng-if="!addingAgentToLeadType[stage.id + '_' + leadType.id]">
                                                <button class="btn btn-success btn-sm"
                                                        ng-click="startAddingAgentToLeadType(stage.id, leadType.id)"
                                                        title="Add new agent to this stage/lead type">
                                                    <i class="fa fa-plus"></i>
                                                    Add Agent to {{ leadType.name }}
                                                </button>
                                            </div>

                                            <!-- Inline Agent Addition Form -->
                                            <div ng-if="addingAgentToLeadType[stage.id + '_' + leadType.id]" class="inline-agent-form">
                                                <div class="row">
                                                    <div class="col-md-8 col-sm-12">
                                                        <div class="form-group">
                                                            <label class="control-label">Select Agents:</label>
                                                            <input type="text"
                                                                   class="form-control form-control-sm"
                                                                   ng-model="newAgentSearchLeadType[stage.id + '_' + leadType.id]"
                                                                   placeholder="Search and select agents..."
                                                                   ng-focus="showAgentDropdownLeadType[stage.id + '_' + leadType.id] = true">

                                                            <!-- Agent Multi-Select Dropdown -->
                                                            <div ng-if="showAgentDropdownLeadType[stage.id + '_' + leadType.id]"
                                                                 class="agent-dropdown"
                                                                 ng-click="$event.stopPropagation()">
                                                                <div ng-repeat="agent in getAvailableAgentsForLeadType(stage.id, leadType.id) | filter:newAgentSearchLeadType[stage.id + '_' + leadType.id]"
                                                                     class="agent-dropdown-item"
                                                                     ng-click="toggleAgentSelectionLeadType(stage.id, leadType.id, agent)">
                                                                    <input type="checkbox"
                                                                           ng-checked="selectedNewAgentsLeadType[stage.id + '_' + leadType.id][agent.id]"
                                                                           ng-click="toggleAgentSelectionLeadType(stage.id, leadType.id, agent); $event.stopPropagation()">
                                                                    <span ng-class="{'selected': selectedNewAgentsLeadType[stage.id + '_' + leadType.id][agent.id]}">
                                                                        {{ agent.name }}
                                                                    </span>
                                                                </div>
                                                            </div>

                                                            <!-- Selected Agents Display -->
                                                            <div ng-if="getSelectedAgentsLeadTypeDisplay(stage.id, leadType.id).length > 0" class="selected-agents">
                                                                <span ng-repeat="agent in getSelectedAgentsLeadTypeDisplay(stage.id, leadType.id)"
                                                                      class="agent-tag">
                                                                    {{ agent.name }}
                                                                    <i class="fa fa-times" ng-click="removeAgentSelectionLeadType(stage.id, leadType.id, agent.id)"></i>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-md-4 col-sm-12">
                                                        <div class="form-group">
                                                            <label class="control-label">&nbsp;</label>
                                                            <div class="button-group">
                                                                <button class="btn btn-primary btn-xs btn-block"
                                                                        ng-click="addAgentsToLeadTypeInline(stage.id, leadType.id)"
                                                                        ng-disabled="getSelectedAgentsLeadTypeDisplay(stage.id, leadType.id).length === 0"
                                                                        title="Add selected agents"
                                                                        style="margin-bottom: 5px;">
                                                                    <i class="fa fa-check"></i>
                                                                    Add ({{ getSelectedAgentsLeadTypeDisplay(stage.id, leadType.id).length }})
                                                                </button>
                                                                <button class="btn btn-white btn-xs btn-block"
                                                                        ng-click="cancelAddingAgentToLeadType(stage.id, leadType.id)"
                                                                        title="Cancel">
                                                                    <i class="fa fa-times"></i>
                                                                    Cancel
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Selected Agent Preview -->
                                                <div ng-if="selectedNewAgentLeadType[stage.id + '_' + leadType.id]" class="selected-agent-preview">
                                                    <strong>Selected:</strong> {{ selectedNewAgentLeadType[stage.id + '_' + leadType.id].name }}
                                                    <span class="text-info">(will be assigned to {{ leadType.name }})</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Third View: Agent → Stage → Lead Types -->
            <div ng-if="assignmentMode === 'agentStage'" class="assignment-view">



                <!-- Agent-Centric View -->
                <div class="agents-view">
                    <div ng-repeat="agent in agentAssignments" class="agent-section">
                        <div class="agent-header-row">
                            <button class="btn btn-xs btn-link tree-toggle"
                                    ng-click="toggleAgentExpansion(agent.id)">
                                <i class="fa" ng-class="isAgentExpanded(agent.id) ? 'fa-minus-square-o' : 'fa-plus-square-o'"></i>
                            </button>

                            <div class="agent-info">
                                <h5 class="agent-name">
                                    <i class="fa fa-user text-success"></i>
                                    {{ agent.name }}
                                </h5>
                            </div>

                            <div class="agent-summary">
                                <span class="assigned-count">
                                    {{ agent.assignedStages.length }} stage(s) assigned
                                </span>
                            </div>
                        </div>

                        <!-- Agent's Stage Assignments (Expandable) -->
                        <div ng-if="isAgentExpanded(agent.id)" class="agent-stages-list">

                            <!-- Default text when no stages assigned -->
                            <div ng-if="agent.assignedStages.length === 0" class="no-agents-message">
                                <i class="fa fa-info-circle text-muted"></i>
                                <span class="text-muted">{{ agent.name }} is not assigned to any stages yet. Click "Add {{ agent.name }} to Stages" to get started.</span>
                            </div>

                            <!-- Show assigned stages -->
                            <div ng-repeat="stageAssignment in agent.assignedStages" class="stage-assignment-row">
                                <!-- Normal Stage Display -->
                                <div ng-if="!editingAgentStageLeadTypes[agent.id + '_' + stageAssignment.stage.id]" class="stage-assignment-display">
                                    <div class="stage-info">
                                        <span class="stage-name">
                                            <i class="fa fa-folder text-primary"></i>
                                            {{ stageAssignment.stage.name }}
                                        </span>
                                    </div>

                                    <div class="stage-leadtypes">
                                        <strong>Lead Types:</strong>
                                        <span ng-repeat="leadType in stageAssignment.assignedLeadTypes" class="label label-success" style="margin: 2px;">
                                            {{ leadType.name }}
                                        </span>
                                        <span ng-if="stageAssignment.assignedLeadTypes.length === 0" class="text-muted">
                                            All lead types (no specific assignment)
                                        </span>
                                    </div>

                                    <div class="stage-actions">
                                        <button class="btn btn-xs btn-primary"
                                                ng-click="startEditingAgentStageLeadTypes(agent.id, stageAssignment.stage.id)"
                                                title="Edit lead type assignments for this stage">
                                            <i class="fa fa-edit"></i>
                                            Edit Lead Types
                                        </button>
                                        <button class="btn btn-xs btn-danger"
                                                ng-click="removeAgentFromStageAssignment(agent.id, stageAssignment.stage.id)"
                                                title="Remove agent from this stage">
                                            <i class="fa fa-times"></i>
                                            Remove
                                        </button>
                                    </div>
                                </div>

                                <!-- Inline Lead Type Editor for Agent-Stage -->
                                <div ng-if="editingAgentStageLeadTypes[agent.id + '_' + stageAssignment.stage.id]" class="inline-leadtype-editor full-width-editor">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="agent-stage-info-edit">
                                                <span class="agent-name">
                                                    <i class="fa fa-user text-success"></i>
                                                    {{ agent.name }}
                                                </span>
                                                <span class="stage-name">
                                                    <i class="fa fa-folder text-primary"></i>
                                                    {{ stageAssignment.stage.name }}
                                                </span>
                                                <small class="text-muted">Editing lead types</small>
                                            </div>
                                        </div>

                                        <div class="col-md-7">
                                            <div class="leadtype-editor">
                                                <label class="control-label">Select Lead Types:</label>
                                                <input type="text"
                                                       class="form-control form-control-sm"
                                                       ng-model="editAgentStageLeadTypeSearch[agent.id + '_' + stageAssignment.stage.id]"
                                                       placeholder="Search lead types..."
                                                       ng-focus="showEditAgentStageLeadTypeDropdown[agent.id + '_' + stageAssignment.stage.id] = true">

                                                <!-- Edit Lead Type Dropdown -->
                                                <div ng-if="showEditAgentStageLeadTypeDropdown[agent.id + '_' + stageAssignment.stage.id]"
                                                     class="leadtype-dropdown"
                                                     ng-click="$event.stopPropagation()">
                                                    <div ng-repeat="leadType in leadTypes | filter:editAgentStageLeadTypeSearch[agent.id + '_' + stageAssignment.stage.id]"
                                                         class="leadtype-dropdown-item"
                                                         ng-click="toggleEditAgentStageLeadTypeSelection(agent.id, stageAssignment.stage.id, leadType)">
                                                        <input type="checkbox"
                                                               ng-checked="editingAgentStageLeadTypes[agent.id + '_' + stageAssignment.stage.id][leadType.id]"
                                                               ng-click="toggleEditAgentStageLeadTypeSelection(agent.id, stageAssignment.stage.id, leadType); $event.stopPropagation()">
                                                        <span ng-class="{'selected': editingAgentStageLeadTypes[agent.id + '_' + stageAssignment.stage.id][leadType.id]}">
                                                            {{ leadType.name }}
                                                        </span>
                                                    </div>
                                                </div>

                                                <!-- Selected Lead Types Display -->
                                                <div ng-if="getEditingAgentStageLeadTypesDisplay(agent.id, stageAssignment.stage.id).length > 0" class="selected-leadtypes-scrollable">
                                                    <span ng-repeat="leadType in getEditingAgentStageLeadTypesDisplay(agent.id, stageAssignment.stage.id)"
                                                          class="leadtype-tag">
                                                        {{ leadType.name }}
                                                        <i class="fa fa-times" ng-click="removeEditAgentStageLeadTypeSelection(agent.id, stageAssignment.stage.id, leadType.id)"></i>
                                                    </span>
                                                </div>

                                                <small class="text-muted">Leave empty to assign ALL lead types</small>
                                            </div>
                                        </div>

                                        <div class="col-md-2">
                                            <div class="edit-actions">
                                                <button class="btn btn-primary btn-xs btn-block"
                                                        ng-click="saveAgentStageLeadTypes(agent.id, stageAssignment.stage.id)"
                                                        title="Save changes"
                                                        style="margin-bottom: 5px;">
                                                    <i class="fa fa-check"></i>
                                                    Save
                                                </button>
                                                <button class="btn btn-white btn-xs btn-block"
                                                        ng-click="cancelEditingAgentStageLeadTypes(agent.id, stageAssignment.stage.id)"
                                                        title="Cancel editing">
                                                    <i class="fa fa-times"></i>
                                                    Cancel
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Add Agent to Stages Row -->
                            <div class="add-agent-row">
                                <div ng-if="!addingAgentToStages[agent.id]">
                                    <button class="btn btn-success btn-xs"
                                            ng-click="startAddingAgentToStages(agent.id)"
                                            title="Add this agent to additional stages">
                                        <i class="fa fa-plus"></i>
                                        Add to Stages
                                    </button>
                                </div>

                                <!-- Inline Agent-to-Stages Addition Form -->
                                <div ng-if="addingAgentToStages[agent.id]" class="inline-agent-form">
                                    <div class="row">
                                        <div class="col-md-5">
                                            <div class="form-group">
                                                <label class="control-label">Select Stages:</label>
                                                <input type="text"
                                                       class="form-control form-control-sm"
                                                       ng-model="newAgentStageSearch[agent.id]"
                                                       placeholder="Search and select stages..."
                                                       ng-focus="showAgentStageDropdown[agent.id] = true">

                                                <!-- Stage Multi-Select Dropdown -->
                                                <div ng-if="showAgentStageDropdown[agent.id]"
                                                     class="leadtype-dropdown"
                                                     ng-click="$event.stopPropagation()">
                                                    <div ng-repeat="stage in getAvailableStagesForAgent(agent.id) | filter:newAgentStageSearch[agent.id]"
                                                         class="leadtype-dropdown-item"
                                                         ng-click="toggleAgentStageSelection(agent.id, stage)">
                                                        <input type="checkbox"
                                                               ng-checked="selectedAgentStages[agent.id][stage.id]"
                                                               ng-click="toggleAgentStageSelection(agent.id, stage); $event.stopPropagation()">
                                                        <span ng-class="{'selected': selectedAgentStages[agent.id][stage.id]}">
                                                            {{ stage.name }}
                                                        </span>
                                                    </div>
                                                </div>

                                                <!-- Selected Stages Display -->
                                                <div ng-if="getSelectedAgentStagesDisplay(agent.id).length > 0" class="selected-leadtypes">
                                                    <span ng-repeat="stage in getSelectedAgentStagesDisplay(agent.id)"
                                                          class="leadtype-tag">
                                                        {{ stage.name }}
                                                        <i class="fa fa-times" ng-click="removeAgentStageSelection(agent.id, stage.id)"></i>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-5">
                                            <div class="form-group">
                                                <label class="control-label">Select Lead Types:</label>
                                                <input type="text"
                                                       class="form-control form-control-sm"
                                                       ng-model="newAgentLeadTypeSearch[agent.id]"
                                                       placeholder="Search and select lead types..."
                                                       ng-focus="showAgentLeadTypeDropdown[agent.id] = true">

                                                <!-- Lead Type Multi-Select Dropdown -->
                                                <div ng-if="showAgentLeadTypeDropdown[agent.id]"
                                                     class="leadtype-dropdown"
                                                     ng-click="$event.stopPropagation()">
                                                    <div ng-repeat="leadType in leadTypes | filter:newAgentLeadTypeSearch[agent.id]"
                                                         class="leadtype-dropdown-item"
                                                         ng-click="toggleAgentLeadTypeSelection(agent.id, leadType)">
                                                        <input type="checkbox"
                                                               ng-checked="selectedAgentLeadTypes[agent.id][leadType.id]"
                                                               ng-click="toggleAgentLeadTypeSelection(agent.id, leadType); $event.stopPropagation()">
                                                        <span ng-class="{'selected': selectedAgentLeadTypes[agent.id][leadType.id]}">
                                                            {{ leadType.name }}
                                                        </span>
                                                    </div>
                                                </div>

                                                <!-- Selected Lead Types Display -->
                                                <div ng-if="getSelectedAgentLeadTypesDisplay(agent.id).length > 0" class="selected-leadtypes-scrollable">
                                                    <span ng-repeat="leadType in getSelectedAgentLeadTypesDisplay(agent.id)"
                                                          class="leadtype-tag">
                                                        {{ leadType.name }}
                                                        <i class="fa fa-times" ng-click="removeAgentLeadTypeSelection(agent.id, leadType.id)"></i>
                                                    </span>
                                                </div>

                                                <small class="text-muted">Leave empty to assign ALL lead types</small>
                                            </div>
                                        </div>

                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <div class="button-group">
                                                    <button class="btn btn-primary btn-xs btn-block"
                                                            ng-click="addAgentToStagesInline(agent.id)"
                                                            ng-disabled="getSelectedAgentStagesDisplay(agent.id).length === 0"
                                                            title="Add agent to selected stages"
                                                            style="margin-bottom: 5px;">
                                                        <i class="fa fa-check"></i>
                                                        Add ({{ getSelectedAgentStagesDisplay(agent.id).length }})
                                                    </button>
                                                    <button class="btn btn-white btn-xs btn-block"
                                                            ng-click="cancelAddingAgentToStages(agent.id)"
                                                            title="Cancel">
                                                        <i class="fa fa-times"></i>
                                                        Cancel
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bulk Assignment Section -->
            <div class="bulk-assignment-section" ng-if="agents.length > 0 && stages.length > 0">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5>
                            <i class="fa fa-plus-circle text-success"></i>
                            Bulk Assignment: Add Agents to Multiple Stages & Lead Types
                        </h5>
                    </div>
                    <div class="panel-body">
                        <div class="inline-agent-form">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label">Select Agents:</label>
                                        <input type="text"
                                               class="form-control form-control-sm"
                                               ng-model="bulkAgentSearch"
                                               placeholder="Search and select agents..."
                                               ng-focus="showBulkAgentDropdown = true">

                                        <!-- Bulk Agent Multi-Select Dropdown -->
                                        <div ng-if="showBulkAgentDropdown"
                                             class="agent-dropdown"
                                             ng-click="$event.stopPropagation()">
                                            <div ng-repeat="agent in agents | filter:bulkAgentSearch"
                                                 class="agent-dropdown-item"
                                                 ng-click="toggleBulkAgentSelection(agent)">
                                                <input type="checkbox"
                                                       ng-checked="selectedBulkAgents[agent.id]"
                                                       ng-click="toggleBulkAgentSelection(agent); $event.stopPropagation()">
                                                <span ng-class="{'selected': selectedBulkAgents[agent.id]}">
                                                    {{ agent.name }}
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Selected Agents Display -->
                                        <div ng-if="getBulkSelectedAgentsDisplay().length > 0" class="selected-agents">
                                            <span ng-repeat="agent in getBulkSelectedAgentsDisplay()"
                                                  class="agent-tag">
                                                {{ agent.name }}
                                                <i class="fa fa-times" ng-click="removeBulkAgentSelection(agent.id)"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label">Select Stages:</label>
                                        <input type="text"
                                               class="form-control form-control-sm"
                                               ng-model="bulkStageSearch"
                                               placeholder="Search and select stages..."
                                               ng-focus="showBulkStageDropdown = true">

                                        <!-- Bulk Stage Multi-Select Dropdown -->
                                        <div ng-if="showBulkStageDropdown"
                                             class="leadtype-dropdown"
                                             ng-click="$event.stopPropagation()">
                                            <div ng-repeat="stage in stages | filter:bulkStageSearch"
                                                 class="leadtype-dropdown-item"
                                                 ng-click="toggleBulkStageSelection(stage)">
                                                <input type="checkbox"
                                                       ng-checked="selectedBulkStages[stage.id]"
                                                       ng-click="toggleBulkStageSelection(stage); $event.stopPropagation()">
                                                <span ng-class="{'selected': selectedBulkStages[stage.id]}">
                                                    {{ stage.name }}
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Selected Stages Display -->
                                        <div ng-if="getBulkSelectedStagesDisplay().length > 0" class="selected-leadtypes">
                                            <span ng-repeat="stage in getBulkSelectedStagesDisplay()"
                                                  class="leadtype-tag">
                                                {{ stage.name }}
                                                <i class="fa fa-times" ng-click="removeBulkStageSelection(stage.id)"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label">Select Lead Types:</label>
                                        <input type="text"
                                               class="form-control form-control-sm"
                                               ng-model="bulkLeadTypeSearch"
                                               placeholder="Search and select lead types..."
                                               ng-focus="showBulkLeadTypeDropdown = true">

                                        <!-- Bulk Lead Type Multi-Select Dropdown -->
                                        <div ng-if="showBulkLeadTypeDropdown"
                                             class="leadtype-dropdown"
                                             ng-click="$event.stopPropagation()">
                                            <div ng-repeat="leadType in leadTypes | filter:bulkLeadTypeSearch"
                                                 class="leadtype-dropdown-item"
                                                 ng-click="toggleBulkLeadTypeSelection(leadType)">
                                                <input type="checkbox"
                                                       ng-checked="selectedBulkLeadTypes[leadType.id]"
                                                       ng-click="toggleBulkLeadTypeSelection(leadType); $event.stopPropagation()">
                                                <span ng-class="{'selected': selectedBulkLeadTypes[leadType.id]}">
                                                    {{ leadType.name }}
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Selected Lead Types Display -->
                                        <div ng-if="getBulkSelectedLeadTypesDisplay().length > 0" class="selected-leadtypes-scrollable">
                                            <span ng-repeat="leadType in getBulkSelectedLeadTypesDisplay()"
                                                  class="leadtype-tag">
                                                {{ leadType.name }}
                                                <i class="fa fa-times" ng-click="removeBulkLeadTypeSelection(leadType.id)"></i>
                                            </span>
                                        </div>

                                        <small class="text-muted">Leave empty to assign ALL lead types</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12 text-center">
                                    <button class="btn btn-success btn-sm"
                                            ng-click="performBulkAssignment()"
                                            ng-disabled="getBulkSelectedAgentsDisplay().length === 0 || getBulkSelectedStagesDisplay().length === 0"
                                            title="Create bulk assignments">
                                        <i class="fa fa-check"></i>
                                        Create Assignments
                                        ({{ getBulkSelectedAgentsDisplay().length }} agents × {{ getBulkSelectedStagesDisplay().length }} stages)
                                    </button>
                                    <button class="btn btn-white btn-sm"
                                            ng-click="clearBulkSelections()"
                                            title="Clear all selections">
                                        <i class="fa fa-times"></i>
                                        Clear All
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Close assignment editor div -->
        </div>

        <!-- Empty state -->
        <div ng-if="currentViewData.length === 0 && currentView !== 'assignmentEditor'" class="empty-state text-center">
            <i class="fa fa-users fa-3x text-muted"></i>
            <h4 class="text-muted">No Data Available</h4>
            <p class="text-muted">
                No agent assignment data found for this campaign.
                <br>
                Please ensure the campaign has stages and lead types configured.
            </p>
        </div>
        
        <!-- Saving indicator -->
        <div ng-if="saving" class="saving-overlay">
            <div class="saving-content">
                <i class="fa fa-spinner fa-spin fa-2x"></i>
                <p>Saving changes...</p>
            </div>
        </div>
    </div>

    <!-- Modals removed - using inline editing instead -->
</div>

<style>
.agent-assignment-container {
    position: relative;
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.view-switcher-container {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.view-switcher-container h4 {
    margin-bottom: 15px;
    color: #495057;
    font-weight: 600;
}

.view-switcher .btn {
    border-radius: 0;
    border-color: #007bff;
    color: #007bff;
    background: #fff;
    transition: all 0.2s ease;
}

.view-switcher .btn:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.view-switcher .btn:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.view-switcher .btn.active {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

.view-switcher .btn:hover:not(.active) {
    background-color: #f8f9fa;
}

.view-description {
    margin-top: 10px;
    font-size: 0.9em;
}

.action-buttons-container {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.selection-controls {
    display: flex;
    align-items: center;
}

.selection-count {
    margin-left: 10px;
    font-size: 0.9em;
}

.mass-action-buttons .btn {
    margin-left: 5px;
}

.tree-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: #fff;
}

.tree-wrapper {
    padding: 15px;
}

.empty-state {
    padding: 60px 20px;
}

.saving-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.saving-content {
    text-align: center;
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .view-switcher .btn {
        font-size: 0.8em;
        padding: 6px 8px;
    }
    
    .action-buttons-container .row > div {
        margin-bottom: 10px;
    }
    
    .action-buttons-container .text-right {
        text-align: left !important;
    }
    
    .mass-action-buttons .btn {
        margin-left: 0;
        margin-right: 5px;
        margin-bottom: 5px;
    }
}

/* Assignment Matrix Styles */
.assignment-matrix {
    margin-top: 20px;
}

.matrix-header {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

.assignment-table {
    font-size: 12px;
}

.assignment-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #dee2e6;
}

.stage-header, .leadtype-header {
    background-color: #e9ecef !important;
    min-width: 120px;
}

.agents-header {
    background-color: #d1ecf1 !important;
}

.agent-header {
    background-color: #d1ecf1 !important;
    min-width: 100px;
}

.actions-header {
    background-color: #f8d7da !important;
    min-width: 80px;
}

.assignment-table td {
    vertical-align: middle;
    border: 1px solid #dee2e6;
}

.stage-cell, .leadtype-cell {
    background-color: #f8f9fa;
    font-weight: 500;
}

.agent-cell {
    background-color: #ffffff;
    padding: 8px;
}

.checkbox-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.assignment-checkbox {
    transform: scale(1.2);
    margin: 0;
}

.assignment-status .label {
    font-size: 10px;
    padding: 2px 6px;
}

.actions-cell {
    background-color: #fff5f5;
}

.bulk-actions {
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #28a745;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.checkbox-inline {
    margin-right: 15px;
    font-weight: normal;
}

/* Row highlighting */
.assignment-table tbody tr.warning {
    background-color: #fff3cd;
}

.assignment-table tbody tr.success {
    background-color: #d4edda;
}

.assignment-table tbody tr:hover {
    background-color: #f5f5f5;
}

/* Assignment Tree Styles */
.assignment-tree-container {
    margin-top: 20px;
}

.assignment-mode-selector {
    margin-bottom: 20px;
}

.assignment-mode-selector .btn-group {
    margin-bottom: 20px;
}

/* Enhanced view mode selector */
.view-mode-selector {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 6px;
    overflow: hidden;
}

.view-mode-selector .btn {
    font-weight: 600;
    padding: 12px 20px;
    font-size: 14px;
    border-radius: 0;
    transition: all 0.3s ease;
    position: relative;
}

.view-mode-selector .btn:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
}

.view-mode-selector .btn:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}

.view-mode-selector .btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.view-mode-selector .btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.view-mode-selector .btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.view-mode-selector .btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    transform: translateY(-1px);
}

.view-mode-selector .btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
    transform: translateY(-1px);
}

.view-mode-selector .btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
    transform: translateY(-1px);
}

.mode-description {
    margin-top: 10px;
}

.assignment-tree {
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background-color: #ffffff;
}

.tree-stage-node {
    border-bottom: 1px solid #e9ecef;
}

.tree-stage-node:last-child {
    border-bottom: none;
}

.stage-header-row {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.stage-header-row:hover {
    background-color: #e9ecef;
}

.tree-toggle {
    padding: 8px;
    margin-right: 12px;
    border: 1px solid #dee2e6;
    background: #ffffff;
    color: #495057;
    width: 32px;
    height: 32px;
    text-align: center;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tree-toggle:hover {
    color: #007bff;
    border-color: #007bff;
    background-color: #f8f9fa;
    transform: scale(1.05);
}

.tree-toggle:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.tree-toggle i {
    font-size: 16px;
}

.stage-info {
    flex-grow: 1;
    margin-right: 15px;
}

.stage-name {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.stage-actions {
    margin-right: 15px;
}

.stage-actions .btn {
    margin-left: 5px;
}

.stage-summary {
    font-size: 12px;
    color: #6c757d;
    min-width: 120px;
    text-align: right;
}

.assigned-count {
    font-weight: 600;
}

.agents-list {
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
    width: 100%;
}

.agent-assignment-row {
    display: flex;
    align-items: center;
    padding: 8px 15px 8px 45px;
    border-bottom: 1px solid #f8f9fa;
    width: 100%;
    box-sizing: border-box;
}

.agent-assignment-row:hover {
    background-color: #f8f9fa;
}

.agent-assignment-row:last-child {
    border-bottom: none;
}

.agent-checkbox {
    margin-right: 12px;
}

.agent-info {
    flex: 0 0 auto;
    margin-right: 15px;
    min-width: 150px;
}

.agent-name {
    font-weight: 500;
    margin-right: 8px;
}

.assignment-status {
    min-width: 100px;
    text-align: right;
}

.assignment-status .label {
    font-size: 10px;
    padding: 2px 6px;
    margin-left: 3px;
}

/* Lead Type Nodes */
.leadtypes-list {
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
}

.tree-leadtype-node {
    border-bottom: 1px solid #f8f9fa;
    margin-left: 30px;
}

.tree-leadtype-node:last-child {
    border-bottom: none;
}

.leadtype-header-row {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.leadtype-header-row:hover {
    background-color: #e9ecef;
}

.leadtype-info {
    flex-grow: 1;
    margin-right: 15px;
}

.leadtype-name {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.leadtype-actions {
    margin-right: 15px;
}

.leadtype-actions .btn {
    margin-left: 5px;
}

.leadtype-summary {
    font-size: 12px;
    color: #6c757d;
    min-width: 120px;
    text-align: right;
}

/* Enhanced Agent Lead Types Display */
.agent-leadtypes {
    flex: 1;
    margin: 5px 15px 5px 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px;
    max-height: 60px;
    overflow-y: auto;
    min-width: 0; /* Allow shrinking */
}

.agent-leadtypes strong {
    margin-right: 8px;
    white-space: nowrap;
    font-size: 12px;
    flex-shrink: 0;
}

.agent-leadtypes .label {
    font-size: 10px;
    padding: 2px 6px;
    background-color: #007bff;
    color: white;
    border-radius: 3px;
    margin: 1px;
    display: inline-block;
    font-weight: normal;
    white-space: nowrap;
}

.agent-leadtypes .text-muted {
    font-style: italic;
    font-size: 11px;
}

/* Selected Agents Display */
.selected-agents {
    margin-top: 8px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.agent-tag {
    display: inline-block;
    background-color: #28a745;
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    margin: 2px;
    font-size: 11px;
    font-weight: normal;
}

.agent-tag i {
    margin-left: 5px;
    cursor: pointer;
    opacity: 0.7;
}

.agent-tag i:hover {
    opacity: 1;
}

/* No agents message */
.no-agents-message {
    padding: 20px;
    text-align: center;
    font-style: italic;
    color: #6c757d;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin: 10px 0;
}

.no-agents-message i {
    margin-right: 8px;
    font-size: 16px;
}

/* Third View: Agent → Stage → Lead Types */
.agent-section {
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background-color: #ffffff;
}

.agent-header-row {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.agent-header-row:hover {
    background-color: #e9ecef;
}

.agent-info {
    flex: 1;
    margin-left: 15px;
}

.agent-info .agent-name {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.agent-summary {
    flex: 0 0 auto;
    margin-left: auto;
    margin-right: 15px;
}

.agent-summary .assigned-count {
    color: #6c757d;
    font-size: 12px;
    font-weight: normal;
}

.agent-stages-list {
    background-color: #ffffff;
}

.stage-assignment-row {
    display: flex;
    align-items: center;
    padding: 10px 15px 10px 45px;
    border-bottom: 1px solid #f8f9fa;
}

.stage-assignment-row:last-child {
    border-bottom: none;
}

.stage-info {
    flex: 0 0 200px;
    min-width: 150px;
}

.stage-info .stage-name {
    font-weight: 600;
    font-size: 14px;
}

.stage-leadtypes {
    flex: 1;
    margin: 5px 15px 5px 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px;
    max-height: 60px;
    overflow-y: auto;
    min-width: 0;
}

.stage-leadtypes strong {
    margin-right: 8px;
    white-space: nowrap;
    font-size: 12px;
    flex-shrink: 0;
}

.stage-actions {
    flex: 0 0 auto;
    margin-left: auto;
    display: flex;
    gap: 8px;
    align-items: center;
    white-space: nowrap;
}

.bulk-assignment-section {
    margin-bottom: 30px;
}

.bulk-assignment-section .panel-heading {
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.bulk-assignment-section .panel-heading h5 {
    margin: 0;
    color: #155724;
    font-weight: 600;
}

/* Add agent row positioning */
.add-agent-row {
    margin-top: 10px;
    padding: 10px 15px;
    border-top: 1px solid #f8f9fa;
    background-color: #fafafa;
}

.add-agent-row .btn-xs {
    font-size: 11px;
    padding: 2px 8px;
}

/* Agent-Stage Lead Type Editor */
.agent-stage-info-edit {
    padding: 8px 0;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.agent-stage-info-edit .agent-name,
.agent-stage-info-edit .stage-name {
    font-weight: 600;
    display: block;
    font-size: 13px;
}

.agent-stage-info-edit .agent-name {
    color: #28a745;
}

.agent-stage-info-edit .stage-name {
    color: #007bff;
}

.stage-assignment-display {
    display: flex;
    align-items: center;
    width: 100%;
}

.full-width-editor {
    width: 100%;
    margin: 0 -15px; /* Extend beyond container padding */
    padding: 15px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
}

.agent-actions {
    flex: 0 0 auto;
    margin-left: auto; /* Push to the right */
    display: flex;
    gap: 8px;
    align-items: center;
    white-space: nowrap;
}

.agent-actions .btn {
    margin-left: 0; /* Remove individual margins, use gap instead */
}

/* Add Agent Row */
.add-agent-row {
    padding: 12px 15px;
    background-color: #e8f4fd;
    border-top: 1px solid #b3d9ff;
    border-bottom: 1px solid #b3d9ff;
    text-align: center;
    margin: 5px 0;
    border-radius: 4px;
}

/* Remove custom button styling - use application defaults */

/* Inline Agent Form */
.inline-agent-form {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 25px;
    margin: 20px 0;
    text-align: left;
    width: 100%;
    max-width: 100%;
    overflow: visible; /* Allow dropdowns to show */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.inline-agent-form .row {
    margin: 0 -20px; /* Add negative margin to offset column padding */
}

.inline-agent-form [class*="col-"] {
    padding: 0 20px; /* Increased padding for better spacing */
}

.inline-agent-form .col-md-4,
.inline-agent-form .col-md-5,
.inline-agent-form .col-md-3,
.inline-agent-form .col-sm-12 {
    padding: 0 20px; /* Increased padding for better spacing */
}

.inline-agent-form .form-group {
    margin-bottom: 20px;
    position: relative; /* For dropdown positioning */
    padding: 10px;
    background-color: #fafafa;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
}

/* White background for button column */
.inline-agent-form .col-md-3 .form-group {
    background-color: #ffffff;
    border: none;
    padding: 10px 0;
}

.inline-agent-form .control-label {
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
    font-size: 12px;
    color: #555;
    text-align: left;
    width: 100%;
}

.inline-agent-form .form-control-sm {
    font-size: 13px;
    padding: 6px 10px;
    width: 100%;
    border-radius: 4px;
}

/* Agent Dropdown */
.agent-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 9999; /* High z-index to appear above other content */
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.agent-dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s;
}

.agent-dropdown-item:hover {
    background-color: #f8f9fa;
}

.agent-dropdown-item:last-child {
    border-bottom: none;
}

/* Lead Type Multi-Select */
.leadtype-multiselect {
    position: relative;
}

.leadtype-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 9998; /* High z-index to appear above other content */
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.leadtype-dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
}

.leadtype-dropdown-item:hover {
    background-color: #f8f9fa;
}

.leadtype-dropdown-item:last-child {
    border-bottom: none;
}

.leadtype-dropdown-item input[type="checkbox"] {
    margin-right: 8px;
}

.leadtype-dropdown-item .selected {
    font-weight: bold;
    color: #007bff;
}

/* Selected Lead Types Tags */
.selected-leadtypes {
    margin-top: 8px;
    padding: 5px;
    background-color: #f8f9fa;
    border-radius: 4px;
    min-height: 30px;
}

.leadtype-tag {
    display: inline-block;
    background-color: #007bff;
    color: white;
    padding: 2px 6px;
    margin: 2px;
    border-radius: 3px;
    font-size: 11px;
    cursor: default;
}

.leadtype-tag i {
    margin-left: 4px;
    cursor: pointer;
    opacity: 0.8;
}

.leadtype-tag i:hover {
    opacity: 1;
}

/* Inline Lead Type Editor */
.inline-leadtype-editor {
    background-color: #fff8e1;
    border: 1px solid #ffcc02;
    border-radius: 4px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: visible;
    min-height: 120px;
    position: relative;
}

.inline-leadtype-editor .row {
    margin: 0 -10px;
    display: flex;
    align-items: flex-start;
}

.inline-leadtype-editor .col-md-3 {
    padding: 0 10px;
    flex: 0 0 25%;
    max-width: 25%;
}

.inline-leadtype-editor .col-md-7 {
    padding: 0 10px;
    flex: 0 0 50%;
    max-width: 50%;
}

.inline-leadtype-editor .col-md-2 {
    padding: 0 5px;
    flex: 0 0 25%;
    max-width: 25%;
    min-width: 80px;
}

.agent-info-edit {
    padding: 8px 0;
}

.agent-info-edit .agent-name {
    font-weight: 600;
    display: block;
    margin-bottom: 3px;
}

.leadtype-editor {
    position: relative;
}

.leadtype-editor .control-label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
    font-size: 12px;
    color: #555;
    text-align: left;
    width: 100%;
}

.edit-actions {
    padding-top: 15px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-width: 100px;
}

.edit-actions .btn {
    margin-bottom: 6px;
    white-space: nowrap;
    min-width: 60px;
    font-size: 10px;
    padding: 3px 6px;
    display: block;
    width: 100%;
    box-sizing: border-box;
}

/* Responsive adjustments for inline editor */
@media (max-width: 1400px) {
    .inline-leadtype-editor .col-md-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }

    .inline-leadtype-editor .col-md-7 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .inline-leadtype-editor .col-md-2 {
        flex: 0 0 25%;
        max-width: 25%;
        min-width: 80px;
    }
}

@media (max-width: 1200px) {
    .inline-leadtype-editor .col-md-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }

    .inline-leadtype-editor .col-md-7 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .inline-leadtype-editor .col-md-2 {
        flex: 0 0 25%;
        max-width: 25%;
        min-width: 75px;
    }
}

@media (max-width: 992px) {
    .inline-leadtype-editor {
        padding: 15px;
    }

    .inline-leadtype-editor .row {
        flex-direction: column;
        gap: 15px;
    }

    .inline-leadtype-editor .col-md-3,
    .inline-leadtype-editor .col-md-7,
    .inline-leadtype-editor .col-md-2 {
        flex: none;
        max-width: 100%;
        min-width: auto;
    }
}

/* Scrollable lead types for handling 30+ types */
.selected-leadtypes-scrollable {
    margin-top: 8px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    max-height: 80px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
}

.selected-leadtypes-scrollable::-webkit-scrollbar {
    width: 6px;
}

.selected-leadtypes-scrollable::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.selected-leadtypes-scrollable::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.selected-leadtypes-scrollable::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Selected Agent Preview */
.selected-agent-preview {
    margin-top: 15px;
    padding: 12px;
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    font-size: 13px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Button improvements */
.inline-agent-form .btn-group-vertical {
    width: 100%;
}

.inline-agent-form .btn-group-vertical .btn {
    margin-bottom: 5px;
}

.inline-agent-form .btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}

.inline-agent-form .button-group {
    width: 100%;
}

.inline-agent-form .button-group .btn {
    margin-bottom: 5px;
}

/* Responsive column adjustments */
@media (max-width: 991px) {
    .inline-agent-form .col-md-4,
    .inline-agent-form .col-md-5,
    .inline-agent-form .col-md-3,
    .inline-agent-form .col-md-8 {
        margin-bottom: 15px;
    }

    .inline-agent-form .button-group .btn {
        margin-bottom: 5px;
    }
}

/* Form positioning */
.form-group {
    position: relative;
}

/* Add some breathing room to the main container */
.agents-list {
    position: relative;
    z-index: 1;
}

/* Ensure dropdowns appear above subsequent rows */
.tree-stage-node {
    position: relative;
    z-index: auto;
}

.tree-stage-node:hover {
    z-index: 2;
}

/* Modal Styles */
.agent-search-results {
    border-radius: 4px;
}

.agent-search-item {
    transition: background-color 0.2s;
}

.agent-search-item:hover {
    background-color: #f8f9fa;
}

.agent-search-item.selected {
    background-color: #007bff;
    color: white;
}

.leadtype-selection {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background-color: #f9f9f9;
}

.leadtype-selection .checkbox {
    margin: 5px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stage-header-row,
    .leadtype-header-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .stage-actions,
    .leadtype-actions,
    .agent-actions {
        margin-top: 10px;
        margin-right: 0;
        margin-left: 0;
    }

    .stage-summary,
    .leadtype-summary {
        margin-top: 5px;
        text-align: left;
        min-width: auto;
    }

    .agent-assignment-row {
        flex-direction: column;
        align-items: flex-start;
        padding-left: 25px;
    }

    .assignment-status {
        margin-top: 5px;
        text-align: left;
        min-width: auto;
    }

    .agent-leadtypes {
        margin-top: 5px;
    }
}
</style>
