<div class="agent-assignment-container">
    <!-- Loading indicator -->
    <div ng-if="loading" class="text-center">
        <i class="fa fa-spinner fa-spin fa-2x"></i>
        <p>Loading agent assignments...</p>
    </div>
    
    <!-- Main content -->
    <div ng-if="!loading">
        <!-- View switcher -->
        <div class="view-switcher-container">
            <h4>Agent Assignment View</h4>
            <div class="btn-group view-switcher" role="group">
                <button 
                    ng-repeat="option in viewOptions"
                    type="button" 
                    class="btn btn-outline-primary"
                    ng-class="{'active': currentView === option.key}"
                    ng-click="switchView(option.key)"
                    title="{{ option.description }}">
                    {{ option.label }}
                </button>
            </div>
            <p class="view-description text-muted">
                {{ (viewOptions | filter:{key: currentView})[0].description }}
            </p>
        </div>
        
        <!-- Action buttons -->
        <div class="action-buttons-container">
            <div class="row">
                <div class="col-md-6">
                    <div class="selection-controls">
                        <label class="checkbox-inline">
                            <input type="checkbox" ng-model="selectAll" ng-change="toggleSelectAll()">
                            Select All
                        </label>
                        <span class="selection-count text-muted">
                            ({{ selectedItems.length }} selected)
                        </span>
                    </div>
                </div>
                <div class="col-md-6 text-right">
                    <div class="mass-action-buttons">
                        <button 
                            class="btn btn-success btn-sm"
                            ng-click="massAssign()"
                            ng-disabled="selectedItems.length === 0 || saving">
                            <i class="fa fa-check"></i>
                            Assign Selected
                        </button>
                        <button 
                            class="btn btn-warning btn-sm"
                            ng-click="massUnassign()"
                            ng-disabled="selectedItems.length === 0 || saving">
                            <i class="fa fa-times"></i>
                            Unassign Selected
                        </button>
                        <button 
                            class="btn btn-info btn-sm"
                            ng-click="loadAgentAssignments()"
                            ng-disabled="saving">
                            <i class="fa fa-refresh"></i>
                            Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Hierarchical tree display -->
        <div class="tree-container">
            <div class="tree-wrapper">
                <hierarchical-assignment-tree
                    data="currentViewData"
                    selected-items="selectedItems"
                    expanded-nodes="expandedNodes"
                    depth="0"
                    on-toggle-selection="toggleSelection"
                    on-toggle-expanded="toggleExpanded"
                    is-selected="isSelected"
                    is-expanded="isExpanded"
                    get-assignment-status="getAssignmentStatus"
                    get-assignment-status-class="getAssignmentStatusClass">
                </hierarchical-assignment-tree>
            </div>
        </div>
        
        <!-- Empty state -->
        <div ng-if="currentViewData.length === 0" class="empty-state text-center">
            <i class="fa fa-users fa-3x text-muted"></i>
            <h4 class="text-muted">No Data Available</h4>
            <p class="text-muted">
                No agent assignment data found for this campaign.
                <br>
                Please ensure the campaign has stages and lead types configured.
            </p>
        </div>
        
        <!-- Saving indicator -->
        <div ng-if="saving" class="saving-overlay">
            <div class="saving-content">
                <i class="fa fa-spinner fa-spin fa-2x"></i>
                <p>Saving changes...</p>
            </div>
        </div>
    </div>
</div>

<style>
.agent-assignment-container {
    position: relative;
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.view-switcher-container {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.view-switcher-container h4 {
    margin-bottom: 15px;
    color: #495057;
    font-weight: 600;
}

.view-switcher .btn {
    border-radius: 0;
    border-color: #007bff;
    color: #007bff;
    background: #fff;
    transition: all 0.2s ease;
}

.view-switcher .btn:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.view-switcher .btn:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.view-switcher .btn.active {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

.view-switcher .btn:hover:not(.active) {
    background-color: #f8f9fa;
}

.view-description {
    margin-top: 10px;
    font-size: 0.9em;
}

.action-buttons-container {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.selection-controls {
    display: flex;
    align-items: center;
}

.selection-count {
    margin-left: 10px;
    font-size: 0.9em;
}

.mass-action-buttons .btn {
    margin-left: 5px;
}

.tree-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: #fff;
}

.tree-wrapper {
    padding: 15px;
}

.empty-state {
    padding: 60px 20px;
}

.saving-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.saving-content {
    text-align: center;
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .view-switcher .btn {
        font-size: 0.8em;
        padding: 6px 8px;
    }
    
    .action-buttons-container .row > div {
        margin-bottom: 10px;
    }
    
    .action-buttons-container .text-right {
        text-align: left !important;
    }
    
    .mass-action-buttons .btn {
        margin-left: 0;
        margin-right: 5px;
        margin-bottom: 5px;
    }
}
</style>
