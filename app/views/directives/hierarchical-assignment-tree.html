<div class="hierarchical-tree">
    <ul class="tree-list">
        <li ng-repeat="node in data" class="tree-node">
            <div class="tree-node-content">
                <!-- Expand/Collapse button -->
                <button 
                    ng-if="hasChildren(node)" 
                    class="btn btn-xs btn-link tree-toggle"
                    ng-click="toggleExpanded(node)"
                    type="button">
                    <i class="fa" ng-class="isNodeExpanded(node) ? 'fa-minus-square-o' : 'fa-plus-square-o'"></i>
                </button>
                <span ng-if="!hasChildren(node)" class="tree-spacer"></span>
                
                <!-- Selection checkbox -->
                <input 
                    ng-if="isSelectable(node)"
                    type="checkbox" 
                    ng-checked="isNodeSelected(node)"
                    ng-click="toggleSelection(node)"
                    class="tree-checkbox">
                <span ng-if="!isSelectable(node)" class="tree-spacer"></span>
                
                <!-- Node icon -->
                <i class="fa tree-icon" ng-class="[getNodeIcon(node), getNodeColor(node)]"></i>
                
                <!-- Node label -->
                <span class="tree-label" ng-class="getNodeColor(node)">
                    {{ node.name }}
                </span>
                
                <!-- Assignment status -->
                <span ng-if="node.assigned !== undefined" 
                      class="tree-status" 
                      ng-class="getNodeAssignmentStatusClass(node)">
                    ({{ getNodeAssignmentStatus(node) }})
                </span>
            </div>
            
            <!-- Children -->
            <div ng-if="hasChildren(node) && isNodeExpanded(node)" class="tree-children">
                <hierarchical-assignment-tree
                    data="getChildren(node)"
                    selected-items="selectedItems"
                    expanded-nodes="expandedNodes"
                    depth="depth + 1"
                    on-toggle-selection="onToggleSelection"
                    on-toggle-expanded="onToggleExpanded"
                    is-selected="isSelected"
                    is-expanded="isExpanded"
                    get-assignment-status="getAssignmentStatus"
                    get-assignment-status-class="getAssignmentStatusClass">
                </hierarchical-assignment-tree>
            </div>
        </li>
    </ul>
</div>

<style>
.hierarchical-tree {
    font-family: inherit;
}

.tree-list {
    list-style: none;
    padding-left: 0;
    margin: 0;
}

.tree-node {
    margin: 2px 0;
}

.tree-node-content {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.tree-node-content:hover {
    background-color: #f8f9fa;
}

.tree-toggle {
    padding: 0;
    margin-right: 8px;
    border: none;
    background: none;
    color: #6c757d;
    width: 16px;
    text-align: center;
}

.tree-toggle:hover {
    color: #495057;
}

.tree-spacer {
    width: 24px;
    display: inline-block;
}

.tree-checkbox {
    margin-right: 8px;
    margin-top: 0;
    margin-bottom: 0;
}

.tree-icon {
    margin-right: 8px;
    width: 14px;
    text-align: center;
}

.tree-label {
    font-weight: 500;
    flex-grow: 1;
}

.tree-status {
    font-size: 0.875em;
    font-style: italic;
    margin-left: 8px;
}

.tree-children {
    margin-left: 24px;
    border-left: 1px solid #e9ecef;
    padding-left: 12px;
}

/* Color classes */
.text-primary {
    color: #007bff !important;
}

.text-info {
    color: #17a2b8 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-success {
    color: #28a745 !important;
}

.text-muted {
    color: #6c757d !important;
}
</style>
